import { prisma } from '@/config/database';
import { Branch, User, UserRole } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface CreateBranchData {
  name: string;
  address: string;
  managerId?: string;
}

export interface UpdateBranchData {
  name?: string;
  address?: string;
  managerId?: string;
  isActive?: boolean;
}

export interface BranchListQuery {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BranchPerformanceMetrics {
  totalMembers: number;
  activeLoans: number;
  totalLoanAmount: number;
  collectedAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  savingsAccounts: number;
  totalSavingsAmount: number;
  monthlyCollection: number;
  collectionRate: number;
}

export class BranchService {
  async getBranches(query: BranchListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    branches: (Branch & { manager?: User; _count?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin && userBranchId) {
        where.id = userBranchId;
      }

      // Add search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { address: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Add active status filter
      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      const [branches, total] = await Promise.all([
        prisma.branch.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            manager: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                users: true,
                members: true,
              },
            },
          },
        }),
        prisma.branch.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        branches,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get branches error:', error);
      throw error;
    }
  }

  async createBranch(data: CreateBranchData): Promise<Branch> {
    try {
      // Validate manager if provided
      if (data.managerId) {
        const manager = await prisma.user.findUnique({
          where: { id: data.managerId },
          select: { id: true, role: true, isActive: true },
        });

        if (!manager) {
          throw new Error('Manager not found');
        }

        if (manager.role !== UserRole.manager) {
          throw new Error('Selected user is not a manager');
        }

        if (!manager.isActive) {
          throw new Error('Selected manager is not active');
        }
      }

      const branch = await prisma.branch.create({
        data: {
          name: data.name,
          address: data.address,
          managerId: data.managerId,
          isActive: true,
        },
        include: {
          manager: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info('Branch created', {
        branchId: branch.id,
        name: branch.name,
        managerId: branch.managerId,
      });

      return branch;
    } catch (error) {
      logger.error('Create branch error:', error);
      throw error;
    }
  }

  async getBranchById(id: string, userRole: UserRole, userBranchId?: string): Promise<Branch | null> {
    try {
      // Role-based access control
      if (userRole !== UserRole.admin && userBranchId !== id) {
        throw new Error('Access denied: You can only view your own branch');
      }

      const branch = await prisma.branch.findUnique({
        where: { id },
        include: {
          manager: {
            select: {
              id: true,
              name: true,
              email: true,
              memberId: true,
            },
          },
          _count: {
            select: {
              users: true,
              members: true,
            },
          },
        },
      });

      return branch;
    } catch (error) {
      logger.error('Get branch by ID error:', error);
      throw error;
    }
  }

  async updateBranch(id: string, data: UpdateBranchData): Promise<Branch> {
    try {
      // Validate manager if provided
      if (data.managerId) {
        const manager = await prisma.user.findUnique({
          where: { id: data.managerId },
          select: { id: true, role: true, isActive: true },
        });

        if (!manager) {
          throw new Error('Manager not found');
        }

        if (manager.role !== UserRole.manager) {
          throw new Error('Selected user is not a manager');
        }

        if (!manager.isActive) {
          throw new Error('Selected manager is not active');
        }
      }

      const branch = await prisma.branch.update({
        where: { id },
        data,
        include: {
          manager: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info('Branch updated', {
        branchId: branch.id,
        updatedFields: Object.keys(data),
      });

      return branch;
    } catch (error) {
      logger.error('Update branch error:', error);
      throw error;
    }
  }

  async deleteBranch(id: string): Promise<void> {
    try {
      // Check if branch has users or members
      const branchWithCounts = await prisma.branch.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              users: true,
              members: true,
            },
          },
        },
      });

      if (!branchWithCounts) {
        throw new Error('Branch not found');
      }

      if (branchWithCounts._count.users > 0) {
        throw new Error('Cannot delete branch with active users');
      }

      if (branchWithCounts._count.members > 0) {
        throw new Error('Cannot delete branch with active members');
      }

      await prisma.branch.delete({
        where: { id }
      });

      logger.info('Branch deleted', { branchId: id });
    } catch (error) {
      logger.error('Delete branch error:', error);
      throw error;
    }
  }

  async getBranchUsers(id: string, userRole: UserRole, userBranchId?: string): Promise<User[]> {
    try {
      // Role-based access control
      if (userRole !== UserRole.admin && userBranchId !== id) {
        throw new Error('Access denied: You can only view users from your own branch');
      }

      const users = await prisma.user.findMany({
        where: { branchId: id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          memberId: true,
          isActive: true,
          lastLoginAt: true,
          createdAt: true,
        },
        orderBy: { name: 'asc' },
      });

      return users;
    } catch (error) {
      logger.error('Get branch users error:', error);
      throw error;
    }
  }

  async getBranchMembers(id: string, userRole: UserRole, userBranchId?: string): Promise<any[]> {
    try {
      // Role-based access control
      if (userRole !== UserRole.admin && userBranchId !== id) {
        throw new Error('Access denied: You can only view members from your own branch');
      }

      const members = await prisma.member.findMany({
        where: { branchId: id },
        select: {
          id: true,
          memberId: true,
          name: true,
          phoneNumber: true,
          isActive: true,
          createdAt: true,
        },
        orderBy: { name: 'asc' },
      });

      return members;
    } catch (error) {
      logger.error('Get branch members error:', error);
      throw error;
    }
  }

  async getBranchPerformance(id: string, userRole: UserRole, userBranchId?: string): Promise<BranchPerformanceMetrics> {
    try {
      // Role-based access control
      if (userRole !== UserRole.admin && userBranchId !== id) {
        throw new Error('Access denied: You can only view performance of your own branch');
      }

      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const [
        totalMembers,
        activeLoans,
        loanStats,
        installmentStats,
        savingsStats,
        monthlyCollectionStats
      ] = await Promise.all([
        // Total members
        prisma.member.count({
          where: { branchId: id, isActive: true },
        }),

        // Active loans count
        prisma.loan.count({
          where: {
            loanApplication: {
              member: { branchId: id },
            },
          },
        }),

        // Loan statistics
        prisma.loan.aggregate({
          where: {
            loanApplication: {
              member: { branchId: id },
            },
          },
          _sum: {
            loanAmount: true,
            totalRepaymentAmount: true,
          },
        }),

        // Installment statistics
        prisma.installment.aggregate({
          where: {
            loan: {
              loanApplication: {
                member: { branchId: id },
              },
            },
          },
          _sum: {
            installmentAmount: true,
          },
        }),

        // Savings statistics
        prisma.savingAccount.aggregate({
          where: {
            member: { branchId: id },
            isActive: true,
          },
          _count: true,
          _sum: {
            monthlyAmount: true,
            fdrAmount: true,
          },
        }),

        // Monthly collection
        prisma.installment.aggregate({
          where: {
            loan: {
              loanApplication: {
                member: { branchId: id },
              },
            },
            collectionDate: {
              gte: currentMonth,
            },
            status: 'paid',
          },
          _sum: {
            installmentAmount: true,
          },
        }),
      ]);

      // Calculate collection rate
      const totalDue = installmentStats._sum.installmentAmount || 0;
      const collected = monthlyCollectionStats._sum.installmentAmount || 0;
      const collectionRate = totalDue > 0 ? (collected / totalDue) * 100 : 0;

      return {
        totalMembers,
        activeLoans,
        totalLoanAmount: loanStats._sum.loanAmount || 0,
        collectedAmount: collected,
        pendingAmount: Math.max(0, totalDue - collected),
        overdueAmount: 0, // Would need more complex calculation
        savingsAccounts: savingsStats._count,
        totalSavingsAmount: (savingsStats._sum.monthlyAmount || 0) + (savingsStats._sum.fdrAmount || 0),
        monthlyCollection: collected,
        collectionRate: Math.round(collectionRate * 100) / 100,
      };
    } catch (error) {
      logger.error('Get branch performance error:', error);
      throw error;
    }
  }
}

export const branchService = new BranchService();
