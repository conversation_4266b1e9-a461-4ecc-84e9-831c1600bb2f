import { Router } from 'express';
import { branchController } from '@/controllers/branchController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { UserRole } from '@prisma/client';

const router = Router();

// Validation middleware
const validateBranchId = [
  param('id')
    .isUUID()
    .withMessage('Invalid branch ID format'),
];

const validateCreateBranch = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch name must be between 2 and 100 characters'),
  body('address')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters'),
  body('managerId')
    .optional()
    .isUUID()
    .withMessage('Invalid manager ID format'),
];

const validateUpdateBranch = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch name must be between 2 and 100 characters'),
  body('address')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters'),
  body('managerId')
    .optional()
    .isUUID()
    .withMessage('Invalid manager ID format'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['name', 'address', 'createdAt'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

// All routes require authentication
router.use(authenticate);

// GET /api/branches - Get all branches (admin), get own branch (manager/field officer)
router.get(
  '/',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  branchController.getBranches.bind(branchController)
);

// POST /api/branches - Create new branch (admin only)
router.post(
  '/',
  requireRole([UserRole.admin]),
  validateCreateBranch,
  handleValidationErrors,
  branchController.createBranch.bind(branchController)
);

// GET /api/branches/:id - Get branch details
router.get(
  '/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateBranchId,
  handleValidationErrors,
  branchController.getBranchById.bind(branchController)
);

// PUT /api/branches/:id - Update branch (admin only)
router.put(
  '/:id',
  requireRole([UserRole.admin]),
  validateBranchId,
  validateUpdateBranch,
  handleValidationErrors,
  branchController.updateBranch.bind(branchController)
);

// DELETE /api/branches/:id - Delete branch (admin only)
router.delete(
  '/:id',
  requireRole([UserRole.admin]),
  validateBranchId,
  handleValidationErrors,
  branchController.deleteBranch.bind(branchController)
);

// GET /api/branches/:id/users - Get branch users
router.get(
  '/:id/users',
  requireRole([UserRole.admin, UserRole.manager]),
  validateBranchId,
  handleValidationErrors,
  branchController.getBranchUsers.bind(branchController)
);

// GET /api/branches/:id/members - Get branch members
router.get(
  '/:id/members',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateBranchId,
  handleValidationErrors,
  branchController.getBranchMembers.bind(branchController)
);

// GET /api/branches/:id/performance - Get branch performance metrics
router.get(
  '/:id/performance',
  requireRole([UserRole.admin, UserRole.manager]),
  validateBranchId,
  handleValidationErrors,
  branchController.getBranchPerformance.bind(branchController)
);

export default router;
