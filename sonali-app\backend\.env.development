# Development Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app_dev
DB_USER=root
DB_PASSWORD=

# Prisma Database URL
DATABASE_URL="mysql://root:@localhost:3306/sonali_app_dev"

# JWT Configuration
JWT_SECRET=dev-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=dev-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_SALT_ROUNDS=10

# Rate Limiting (More lenient for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
