import { apiClient } from '../utils/api';
import { API_ENDPOINTS, STORAGE_KEYS } from '../utils/constants';
import { LoginCredentials, RegisterData, User, ApiResponse } from '../types';

export class AuthService {
  async login(credentials: LoginCredentials & { rememberMe?: boolean }): Promise<{
    user: User;
    accessToken: string;
    sessionId: string;
    expiresAt: string;
  }> {
    const response = await apiClient.post<{
      user: User;
      accessToken: string;
      sessionId: string;
      expiresAt: string;
    }>(
      API_ENDPOINTS.AUTH.LOGIN,
      {
        identifier: credentials.email, // Can be email or member ID
        password: credentials.password,
        rememberMe: credentials.rememberMe || false,
      }
    );

    if (response.success && response.data) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.accessToken);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
      localStorage.setItem('sessionId', response.data.sessionId);
      localStorage.setItem('tokenExpiresAt', response.data.expiresAt);
      return response.data;
    }

    throw new Error(response.error || 'Login failed');
  }

  async register(data: RegisterData): Promise<{ user: User; token: string }> {
    const response = await apiClient.post<{ user: User; token: string }>(
      API_ENDPOINTS.AUTH.REGISTER,
      data
    );

    if (response.success && response.data) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.token);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
      return response.data;
    }

    throw new Error(response.error || 'Registration failed');
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearStoredAuth();
    }
  }

  async logoutAll(): Promise<void> {
    try {
      await apiClient.post('/auth/logout-all');
    } catch (error) {
      console.error('Logout all error:', error);
    } finally {
      this.clearStoredAuth();
    }
  }

  async refreshToken(): Promise<{ accessToken: string }> {
    const response = await apiClient.post<{ accessToken: string }>(
      API_ENDPOINTS.AUTH.REFRESH
    );

    if (response.success && response.data) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.accessToken);
      return response.data;
    }

    throw new Error(response.error || 'Token refresh failed');
  }

  async forgotPassword(identifier: string): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      '/auth/forgot-password',
      { identifier }
    );

    if (response.success) {
      return { message: response.message || 'Password reset email sent' };
    }

    throw new Error(response.error || 'Password reset failed');
  }

  async resetPassword(token: string, newPassword: string, confirmPassword: string): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      '/auth/reset-password',
      { token, newPassword, confirmPassword }
    );

    if (response.success) {
      return { message: response.message || 'Password reset successful' };
    }

    throw new Error(response.error || 'Password reset failed');
  }

  async getActiveSessions(): Promise<Array<{
    id: string;
    sessionId: string;
    ipAddress: string;
    userAgent: string;
    isActive: boolean;
    lastActivity: string;
    createdAt: string;
  }>> {
    const response = await apiClient.get('/auth/sessions');

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to get active sessions');
  }

  async revokeSession(sessionId: string): Promise<void> {
    const response = await apiClient.delete(`/auth/sessions/${sessionId}`);

    if (!response.success) {
      throw new Error(response.error || 'Failed to revoke session');
    }
  }

  private clearStoredAuth(): void {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
    localStorage.removeItem('sessionId');
    localStorage.removeItem('tokenExpiresAt');
  }

  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>(API_ENDPOINTS.AUTH.PROFILE);

    if (response.success && response.data) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data));
      return response.data;
    }

    throw new Error(response.error || 'Failed to get profile');
  }

  getStoredToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.TOKEN);
  }

  getStoredUser(): User | null {
    const userStr = localStorage.getItem(STORAGE_KEYS.USER);
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authService = new AuthService();
