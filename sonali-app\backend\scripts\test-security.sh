#!/bin/bash

# Security Testing Script for Sonali App
# This script tests various security features of the authentication system

set -e

# Configuration
API_BASE_URL="http://localhost:3001/api"
TEST_EMAIL="<EMAIL>"
TEST_MEMBER_ID="MEM001"
TEST_PASSWORD="TestPass123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    print_test "$description"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            "$API_BASE_URL$endpoint")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "Status: $status_code (Expected: $expected_status)"
    else
        print_error "Status: $status_code (Expected: $expected_status)"
        echo "Response: $body"
    fi
    
    echo ""
}

# Test with authorization header
test_auth_endpoint() {
    local method=$1
    local endpoint=$2
    local token=$3
    local data=$4
    local expected_status=$5
    local description=$6
    
    print_test "$description"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $token" \
            -d "$data" \
            "$API_BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Authorization: Bearer $token" \
            "$API_BASE_URL$endpoint")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "Status: $status_code (Expected: $expected_status)"
    else
        print_error "Status: $status_code (Expected: $expected_status)"
        echo "Response: $body"
    fi
    
    echo ""
}

print_header "SONALI APP SECURITY TESTING"

# Test 1: Health Check
print_header "1. BASIC CONNECTIVITY"
test_endpoint "GET" "/health" "" "200" "Health check endpoint"

# Test 2: Authentication Tests
print_header "2. AUTHENTICATION TESTS"

# Test login with Member ID
test_endpoint "POST" "/auth/login" \
    '{"identifier":"ADMIN001","password":"admin123","rememberMe":false}' \
    "200" "Login with Member ID"

# Test login with email
test_endpoint "POST" "/auth/login" \
    '{"identifier":"<EMAIL>","password":"admin123","rememberMe":false}' \
    "200" "Login with email"

# Test invalid credentials
test_endpoint "POST" "/auth/login" \
    '{"identifier":"invalid","password":"invalid"}' \
    "401" "Login with invalid credentials"

# Test 3: Rate Limiting Tests
print_header "3. RATE LIMITING TESTS"

print_test "Testing login rate limiting (5 failed attempts)"
for i in {1..6}; do
    echo "Attempt $i:"
    test_endpoint "POST" "/auth/login" \
        '{"identifier":"invalid","password":"invalid"}' \
        "401" "Failed login attempt $i"
    sleep 1
done

# Test 4: Input Validation Tests
print_header "4. INPUT VALIDATION TESTS"

# Test SQL injection attempts
test_endpoint "POST" "/auth/login" \
    '{"identifier":"<EMAIL> OR 1=1","password":"admin123"}' \
    "400" "SQL injection attempt in identifier"

test_endpoint "POST" "/auth/login" \
    '{"identifier":"<EMAIL>","password":"admin123; DROP TABLE users;"}' \
    "400" "SQL injection attempt in password"

# Test XSS attempts
test_endpoint "POST" "/auth/login" \
    '{"identifier":"<script>alert(\"xss\")</script>","password":"admin123"}' \
    "400" "XSS attempt in identifier"

# Test 5: Password Reset Tests
print_header "5. PASSWORD RESET TESTS"

# Test forgot password
test_endpoint "POST" "/auth/forgot-password" \
    '{"identifier":"<EMAIL>"}' \
    "200" "Forgot password with valid email"

# Test forgot password with invalid identifier
test_endpoint "POST" "/auth/forgot-password" \
    '{"identifier":"<EMAIL>"}' \
    "200" "Forgot password with invalid email (should not reveal existence)"

# Test password reset rate limiting
print_test "Testing password reset rate limiting (3 attempts per hour)"
for i in {1..4}; do
    echo "Reset attempt $i:"
    test_endpoint "POST" "/auth/forgot-password" \
        '{"identifier":"<EMAIL>"}' \
        "200" "Password reset attempt $i"
    sleep 1
done

# Test 6: Session Management Tests
print_header "6. SESSION MANAGEMENT TESTS"

# Get a valid token first
print_test "Getting valid access token"
login_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"identifier":"<EMAIL>","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

access_token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -n "$access_token" ]; then
    print_success "Access token obtained"
    
    # Test protected endpoint
    test_auth_endpoint "GET" "/auth/profile" "$access_token" "" "200" "Access protected endpoint with valid token"
    
    # Test get active sessions
    test_auth_endpoint "GET" "/auth/sessions" "$access_token" "" "200" "Get active sessions"
    
    # Test logout
    test_auth_endpoint "POST" "/auth/logout" "$access_token" "" "200" "Logout current session"
    
    # Test accessing protected endpoint after logout
    test_auth_endpoint "GET" "/auth/profile" "$access_token" "" "401" "Access protected endpoint after logout"
else
    print_error "Failed to obtain access token"
fi

# Test 7: Security Headers Tests
print_header "7. SECURITY HEADERS TESTS"

print_test "Checking security headers"
headers_response=$(curl -s -I "$API_BASE_URL/health")

# Check for important security headers
if echo "$headers_response" | grep -q "X-Content-Type-Options"; then
    print_success "X-Content-Type-Options header present"
else
    print_error "X-Content-Type-Options header missing"
fi

if echo "$headers_response" | grep -q "X-Frame-Options"; then
    print_success "X-Frame-Options header present"
else
    print_error "X-Frame-Options header missing"
fi

if echo "$headers_response" | grep -q "X-XSS-Protection"; then
    print_success "X-XSS-Protection header present"
else
    print_error "X-XSS-Protection header missing"
fi

# Test 8: CORS Tests
print_header "8. CORS TESTS"

# Test CORS with allowed origin
test_endpoint "OPTIONS" "/auth/login" "" "200" "CORS preflight with allowed origin"

# Test 9: Request Size Validation
print_header "9. REQUEST SIZE VALIDATION"

# Create a large payload (over 10MB)
large_payload='{"identifier":"<EMAIL>","password":"admin123","data":"'
for i in {1..1000000}; do
    large_payload+="a"
done
large_payload+='"}'

print_test "Testing request size limit"
response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d "$large_payload" \
    "$API_BASE_URL/auth/login" 2>/dev/null || echo -e "\n413")

status_code=$(echo "$response" | tail -n1)
if [ "$status_code" = "413" ]; then
    print_success "Request size limit enforced (413 Payload Too Large)"
else
    print_error "Request size limit not enforced (Status: $status_code)"
fi

print_header "SECURITY TESTING COMPLETED"
echo -e "${GREEN}All security tests have been executed.${NC}"
echo -e "${YELLOW}Review the results above for any failed tests.${NC}"
echo -e "${BLUE}For production deployment, ensure all tests pass.${NC}"
