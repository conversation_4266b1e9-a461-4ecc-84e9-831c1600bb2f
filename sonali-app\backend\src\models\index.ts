import { sequelize } from '@/config/database';
import { User } from './User';

// Import all models here
export { User };

// Define associations here
export const initializeModels = (): void => {
  // Add model associations here when you have more models
  // Example:
  // User.hasMany(Post, { foreignKey: 'userId', as: 'posts' });
  // Post.belongsTo(User, { foreignKey: 'userId', as: 'author' });
};

// Initialize all models
initializeModels();

export { sequelize };
export default {
  sequelize,
  User,
};
