import { Request, Response } from 'express';
import { authService, LoginCredentials, RegisterData } from '@/services/authService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';

export class AuthController {
  async register(req: Request, res: Response): Promise<void> {
    try {
      const data: RegisterData = req.body;
      const result = await authService.register(data);

      ResponseUtil.created(res, result, 'User registered successfully');
    } catch (error) {
      logger.error('Register controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Registration failed');
    }
  }

  async login(req: Request, res: Response): Promise<void> {
    try {
      const credentials: LoginCredentials = req.body;
      const result = await authService.login(credentials);

      ResponseUtil.success(res, result, 'Login successful');
    } catch (error) {
      logger.error('Login controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Invalid email or password') || 
            error.message.includes('deactivated')) {
          ResponseUtil.unauthorized(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Login failed');
    }
  }

  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        ResponseUtil.error(res, 'Refresh token is required', 400);
        return;
      }

      const result = await authService.refreshToken(refreshToken);

      ResponseUtil.success(res, result, 'Token refreshed successfully');
    } catch (error) {
      logger.error('Refresh token controller error:', error);
      ResponseUtil.unauthorized(res, 'Invalid refresh token');
    }
  }

  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const user = await authService.getProfile(req.userId);

      ResponseUtil.success(res, user, 'Profile retrieved successfully');
    } catch (error) {
      logger.error('Get profile controller error:', error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        ResponseUtil.notFound(res, 'User not found');
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve profile');
    }
  }

  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const data = req.body;
      const user = await authService.updateProfile(req.userId, data);

      ResponseUtil.success(res, user, 'Profile updated successfully');
    } catch (error) {
      logger.error('Update profile controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, 'User not found');
          return;
        }
        if (error.message.includes('already taken')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update profile');
    }
  }

  async changePassword(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const { currentPassword, newPassword } = req.body;
      await authService.changePassword(req.userId, currentPassword, newPassword);

      ResponseUtil.success(res, null, 'Password changed successfully');
    } catch (error) {
      logger.error('Change password controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, 'User not found');
          return;
        }
        if (error.message.includes('incorrect')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to change password');
    }
  }

  async logout(req: Request, res: Response): Promise<void> {
    try {
      // In a more sophisticated implementation, you might want to:
      // 1. Blacklist the token
      // 2. Clear any server-side sessions
      // 3. Log the logout event
      
      if (req.userId) {
        logger.info('User logged out', { userId: req.userId });
      }

      ResponseUtil.success(res, null, 'Logout successful');
    } catch (error) {
      logger.error('Logout controller error:', error);
      ResponseUtil.internalError(res, 'Logout failed');
    }
  }
}

export const authController = new AuthController();
