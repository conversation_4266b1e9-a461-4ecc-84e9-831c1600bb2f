import { Request, Response } from 'express';
import { authService, LoginCredentials, RegisterData } from '@/services/authService';
import { sessionService } from '@/services/sessionService';
import { passwordResetService } from '@/services/passwordResetService';
import { JwtUtil } from '@/utils/jwt';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';

export class AuthController {
  async register(req: Request, res: Response): Promise<void> {
    try {
      const data: RegisterData = req.body;
      const result = await authService.register(data);

      ResponseUtil.created(res, result, 'User registered successfully');
    } catch (error) {
      logger.error('Register controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Registration failed');
    }
  }

  async login(req: Request, res: Response): Promise<void> {
    try {
      const { identifier, password, rememberMe } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      const credentials: LoginCredentials = {
        identifier,
        password,
        rememberMe: rememberMe || false,
        ipAddress,
        userAgent,
      };

      const result = await authService.login(credentials);

      // Set secure HTTP-only cookie for refresh token
      res.cookie('refreshToken', result.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 30 days or 24 hours
      });

      ResponseUtil.success(res, {
        user: result.user,
        accessToken: result.accessToken,
        sessionId: result.sessionId,
        expiresAt: result.expiresAt,
      }, 'Login successful');
    } catch (error) {
      logger.error('Login controller error:', error);

      if (error instanceof Error) {
        if (error.message.includes('Invalid credentials') ||
            error.message.includes('deactivated') ||
            error.message.includes('locked') ||
            error.message.includes('Too many')) {
          ResponseUtil.unauthorized(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Login failed');
    }
  }

  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        ResponseUtil.error(res, 'Refresh token is required', 400);
        return;
      }

      const result = await authService.refreshToken(refreshToken);

      ResponseUtil.success(res, result, 'Token refreshed successfully');
    } catch (error) {
      logger.error('Refresh token controller error:', error);
      ResponseUtil.unauthorized(res, 'Invalid refresh token');
    }
  }

  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const user = await authService.getProfile(req.userId);

      ResponseUtil.success(res, user, 'Profile retrieved successfully');
    } catch (error) {
      logger.error('Get profile controller error:', error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        ResponseUtil.notFound(res, 'User not found');
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve profile');
    }
  }

  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const data = req.body;
      const user = await authService.updateProfile(req.userId, data);

      ResponseUtil.success(res, user, 'Profile updated successfully');
    } catch (error) {
      logger.error('Update profile controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, 'User not found');
          return;
        }
        if (error.message.includes('already taken')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update profile');
    }
  }

  async changePassword(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const { currentPassword, newPassword } = req.body;
      await authService.changePassword(req.userId, currentPassword, newPassword);

      ResponseUtil.success(res, null, 'Password changed successfully');
    } catch (error) {
      logger.error('Change password controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, 'User not found');
          return;
        }
        if (error.message.includes('incorrect')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to change password');
    }
  }

  async logout(req: Request, res: Response): Promise<void> {
    try {
      const token = JwtUtil.extractTokenFromHeader(req.headers.authorization);

      if (token && req.userId) {
        // Invalidate the access token
        await JwtUtil.invalidateToken(token, 'access', req.userId);

        // Invalidate the session
        if (req.sessionId) {
          await sessionService.invalidateSession(req.sessionId);
        }

        // Clear refresh token cookie
        res.clearCookie('refreshToken');

        logger.info('User logged out', {
          userId: req.userId,
          sessionId: req.sessionId
        });
      }

      ResponseUtil.success(res, null, 'Logout successful');
    } catch (error) {
      logger.error('Logout controller error:', error);
      ResponseUtil.internalError(res, 'Logout failed');
    }
  }

  async logoutAll(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      // Invalidate all user sessions
      await sessionService.invalidateAllUserSessions(req.userId);

      // Increment token version to invalidate all tokens
      await authService.invalidateAllUserTokens(req.userId);

      // Clear refresh token cookie
      res.clearCookie('refreshToken');

      logger.info('User logged out from all devices', { userId: req.userId });

      ResponseUtil.success(res, null, 'Logged out from all devices successfully');
    } catch (error) {
      logger.error('Logout all controller error:', error);
      ResponseUtil.internalError(res, 'Logout from all devices failed');
    }
  }

  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

      if (!refreshToken) {
        ResponseUtil.error(res, 'Refresh token is required', 400);
        return;
      }

      const result = await authService.refreshToken(refreshToken);

      // Set new refresh token cookie
      res.cookie('refreshToken', result.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });

      ResponseUtil.success(res, {
        accessToken: result.accessToken,
      }, 'Token refreshed successfully');
    } catch (error) {
      logger.error('Refresh token controller error:', error);

      // Clear invalid refresh token
      res.clearCookie('refreshToken');

      ResponseUtil.unauthorized(res, 'Invalid refresh token');
    }
  }

  async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      const { identifier } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      const result = await passwordResetService.initiatePasswordReset({
        identifier,
        ipAddress,
        userAgent,
      });

      ResponseUtil.success(res,
        result.resetToken ? { resetToken: result.resetToken } : null,
        result.message
      );
    } catch (error) {
      logger.error('Forgot password controller error:', error);
      ResponseUtil.internalError(res, 'Failed to process password reset request');
    }
  }

  async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, newPassword, confirmPassword } = req.body;

      const result = await passwordResetService.resetPassword({
        token,
        newPassword,
        confirmPassword,
      });

      if (result.success) {
        ResponseUtil.success(res, null, result.message);
      } else {
        ResponseUtil.error(res, result.message, 400);
      }
    } catch (error) {
      logger.error('Reset password controller error:', error);
      ResponseUtil.internalError(res, 'Failed to reset password');
    }
  }

  async getActiveSessions(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const sessions = await sessionService.getUserActiveSessions(req.userId);

      ResponseUtil.success(res, sessions, 'Active sessions retrieved successfully');
    } catch (error) {
      logger.error('Get active sessions controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve active sessions');
    }
  }

  async revokeSession(req: Request, res: Response): Promise<void> {
    try {
      if (!req.userId) {
        ResponseUtil.unauthorized(res, 'User not authenticated');
        return;
      }

      const { sessionId } = req.params;

      await sessionService.invalidateSession(sessionId);

      ResponseUtil.success(res, null, 'Session revoked successfully');
    } catch (error) {
      logger.error('Revoke session controller error:', error);
      ResponseUtil.internalError(res, 'Failed to revoke session');
    }
  }
}

export const authController = new AuthController();
