<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for string-template/compile.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">string-template/</a> compile.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>68/68</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">97.67% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>42/43</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>4/4</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>68/68</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">152×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">222×</span>
<span class="cline-any cline-yes">222×</span>
<span class="cline-any cline-yes">146×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">374×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">374×</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">228×</span>
<span class="cline-any cline-yes">82×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">146×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">146×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">184×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">184×</span>
<span class="cline-any cline-yes">111×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">73×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">17×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-yes">187×</span>
<span class="cline-any cline-yes">113×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">74×</span>
<span class="cline-any cline-yes">74×</span>
<span class="cline-any cline-yes">74×</span>
<span class="cline-any cline-yes">74×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">184×</span>
<span class="cline-any cline-yes">184×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var template = require("./index")
&nbsp;
var whitespaceRegex = /["'\\\n\r\u2028\u2029]/g
var nargs = /\{[0-9a-zA-Z]+\}/g
&nbsp;
var replaceTemplate =
"    var args\n" +
"    var result\n" +
"    if (arguments.length === 1 &amp;&amp; typeof arguments[0] === \"object\") {\n" +
"        args = arguments[0]\n" +
"    } else {\n" +
"        args = arguments" +
"    }\n\n" +
"    if (!args || !(\"hasOwnProperty\" in args)) {\n" +
"       args = {}\n" +
"    }\n\n" +
"    return {0}"
&nbsp;
var literalTemplate = "\"{0}\""
var argTemplate = "(result = args.hasOwnProperty(\"{0}\") ? " +
    "args[\"{0}\"] : null, \n        " +
    "(result === null || result === undefined) ? \"\" : result)"
&nbsp;
module.exports = compile
&nbsp;
function compile(string, inline) {
    var replacements = string.match(nargs) || []
    var interleave = string.split(nargs)
    var replace = []
&nbsp;
    for (var i = 0; i &lt; interleave.length; i++) {
        var current = interleave[i]
        var replacement = replacements[i]
        var escapeLeft = current.charAt(current.length - 1)
        var escapeRight = (interleave[i + 1] || "").charAt(0)
&nbsp;
        if (replacement) {
            replacement = replacement.substring(1, replacement.length - 1)
        }
&nbsp;
        if (escapeLeft === "{" &amp;&amp; escapeRight === "}") {
            replace.push(current + replacement)
        } else {
            replace.push(current)
            if (replacement) {
                replace.push({ name: replacement })
            }
        }
    }
&nbsp;
    var prev = [""]
&nbsp;
    for (var j = 0; j &lt; replace.length; j++) {
        var curr = replace[j]
&nbsp;
        if (String(curr) === curr) {
            var top = prev[prev.length - 1]
&nbsp;
            if (String(top) === top) {
                prev[prev.length - 1] = top + curr
            } else {
                prev.push(curr)
            }
        } else {
            prev.push(curr)
        }
    }
&nbsp;
    replace = prev
&nbsp;
    if (inline) {
        for (var k = 0; k &lt; replace.length; k++) {
            var token = replace[k]
&nbsp;
            if (String(token) === token) {
                replace[k] = template(literalTemplate, escape(token))
            } else {
                replace[k] = template(argTemplate, escape(token.name))
            }
        }
&nbsp;
        var replaceCode = replace.join(" +\n    ")
        var compiledSource = template(replaceTemplate, replaceCode)
        return new Function(compiledSource)
    }
&nbsp;
    return function template() {
        var args
&nbsp;
        if (arguments.length === 1 &amp;&amp; typeof arguments[0] === "object") {
            args = arguments[0]
        } else {
            args = arguments
        }
&nbsp;
        if (!args || !("hasOwnProperty" in args)) {
            args = {}
        }
&nbsp;
        var result = []
&nbsp;
        for (var i = 0; i &lt; replace.length; i++) {
            if (i % 2 === 0) {
                result.push(replace[i])
            } else {
                var argName = replace[i].name
                var arg = args.hasOwnProperty(argName) ? args[argName] : null
                <span class="missing-if-branch" title="else path not taken" >E</span>if (arg !== null || arg !== undefined) {
                    result.push(arg)
                }
            }
        }
&nbsp;
        return result.join("")
    }
}
&nbsp;
function escape(string) {
    string = '' + string
    return string.replace(whitespaceRegex, escapedWhitespace)
}
&nbsp;
function escapedWhitespace(character) {
    // Escape all characters not included in SingleStringCharacters and
    // DoubleStringCharacters on
    // http://www.ecma-international.org/ecma-262/5.1/#sec-7.8.4
    switch (character) {
        case '"':
        case "'":
        case '\\':
            return '\\' + character
        // Four possible LineTerminator characters need to be escaped:
        case '\n':
            return '\\n'
        case '\r':
            return '\\r'
        case '\u2028':
            return '\\u2028'
        case '\u2029':
            return '\\u2029'
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Thu Jan 07 2016 11:47:09 GMT-0800 (PST)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
