{"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.3.3", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "fengmk2 <<EMAIL>> (http://fengmk2.github.com)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": "mysqljs/sqlstring", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "nyc": "15.1.0", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "node test/run.js", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}