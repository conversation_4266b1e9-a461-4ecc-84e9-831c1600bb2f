// Sonali App Prisma Schema
// Database schema for the complete microfinance management system

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  admin
  manager
  field_officer
  member
}

enum LoanApplicationStatus {
  pending
  approved
  rejected
}

enum RepaymentMethod {
  weekly
  monthly
}

enum InstallmentStatus {
  pending
  paid
  overdue
}

enum EntryType {
  income
  expense
}

enum SavingType {
  general
  dps
  fdr
}

enum SavingMethod {
  daily
  monthly
}

// 1. Branches Table
model Branch {
  id         String   @id @default(cuid())
  name       String
  address    String   @db.Text
  managerId  String?  @map("manager_id")
  isActive   Boolean  @default(true) @map("is_active")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  manager            User?               @relation("BranchManager", fields: [managerId], references: [id])
  users              User[]              @relation("BranchUsers")
  members            Member[]
  branchTransactions BranchTransaction[]

  @@map("branches")
}

// 2. Users Table
model User {
  id              String    @id @default(cuid())
  name            String
  email           String    @unique
  password        String
  isActive        Boolean   @default(true) @map("is_active")
  role            UserRole
  memberId        String?   @unique @map("member_id")
  branchId        String?   @map("branch_id")
  lastLoginAt     DateTime? @map("last_login_at")
  tokenVersion    Int       @default(1) @map("token_version")
  failedLoginAttempts Int   @default(0) @map("failed_login_attempts")
  lockedUntil     DateTime? @map("locked_until")
  passwordResetToken String? @map("password_reset_token")
  passwordResetExpires DateTime? @map("password_reset_expires")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  branch                Branch?             @relation("BranchUsers", fields: [branchId], references: [id])
  managedBranches       Branch[]            @relation("BranchManager")
  createdMembers        Member[]            @relation("MemberCreator")
  reviewedApplications  LoanApplication[]   @relation("ApplicationReviewer")
  collectedInstallments Installment[]       @relation("InstallmentCollector")
  branchTransactions    BranchTransaction[] @relation("TransactionCreator")
  enteredTransactions   BranchTransaction[] @relation("TransactionEnterer")
  createdSavingAccounts SavingAccount[]     @relation("SavingAccountCreator")
  sessions              UserSession[]
  loginAttempts         LoginAttempt[]

  @@map("users")
}

// 3. Members Table
model Member {
  id                    String   @id @default(cuid())
  memberId              String   @unique @map("member_id")
  name                  String
  fatherOrHusbandName   String   @map("father_or_husband_name")
  motherName            String   @map("mother_name")
  presentAddress        String   @db.Text @map("present_address")
  permanentAddress      String   @db.Text @map("permanent_address")
  nidNumber             String   @unique @map("nid_number")
  dateOfBirth           DateTime @map("date_of_birth") @db.Date
  religion              String
  phoneNumber           String   @map("phone_number")
  bloodGroup            String?  @map("blood_group")
  photo                 String?  @db.Text
  occupation            String
  referenceId           String?  @map("reference_id")
  branchId              String   @map("branch_id")
  createdBy             String   @map("created_by")
  isActive              Boolean  @default(true) @map("is_active")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  branch          Branch            @relation(fields: [branchId], references: [id])
  creator         User              @relation("MemberCreator", fields: [createdBy], references: [id])
  reference       Member?           @relation("MemberReference", fields: [referenceId], references: [id])
  references      Member[]          @relation("MemberReference")
  loanApplications LoanApplication[]
  savingAccounts  SavingAccount[]

  @@map("members")
}

// 4. Loan Applications Table
model LoanApplication {
  id               String                @id @default(cuid())
  memberId         String                @map("member_id")
  appliedAmount    Decimal               @map("applied_amount") @db.Decimal(10, 2)
  reason           String                @db.Text
  loanCycleNumber  Int                   @map("loan_cycle_number")
  recommender      String?
  advancePayment   Decimal?              @map("advance_payment") @db.Decimal(10, 2)
  status           LoanApplicationStatus @default(pending)
  reviewedBy       String?               @map("reviewed_by")
  reviewedAt       DateTime?             @map("reviewed_at")
  appliedAt        DateTime              @default(now()) @map("applied_at")
  createdAt        DateTime              @default(now()) @map("created_at")
  updatedAt        DateTime              @updatedAt @map("updated_at")

  // Relations
  member   Member @relation(fields: [memberId], references: [id])
  reviewer User?  @relation("ApplicationReviewer", fields: [reviewedBy], references: [id])
  loan     Loan?

  @@map("loan_applications")
}

// 5. Loans Table
model Loan {
  id                     String          @id @default(cuid())
  loanApplicationId      String          @unique @map("loan_application_id")
  loanDate               DateTime        @map("loan_date") @db.Date
  loanAmount             Decimal         @map("loan_amount") @db.Decimal(10, 2)
  totalRepaymentAmount   Decimal         @map("total_repayment_amount") @db.Decimal(10, 2)
  repaymentDuration      Int             @map("repayment_duration")
  repaymentMethod        RepaymentMethod @map("repayment_method")
  installmentCount       Int             @map("installment_count")
  installmentAmount      Decimal         @map("installment_amount") @db.Decimal(10, 2)
  advancePayment         Decimal?        @map("advance_payment") @db.Decimal(10, 2)
  firstInstallmentDate   DateTime        @map("first_installment_date") @db.Date
  lastInstallmentDate    DateTime        @map("last_installment_date") @db.Date
  createdAt              DateTime        @default(now()) @map("created_at")
  updatedAt              DateTime        @updatedAt @map("updated_at")

  // Relations
  loanApplication LoanApplication @relation(fields: [loanApplicationId], references: [id])
  installments    Installment[]

  @@map("loans")
}

// 6. Installments Table
model Installment {
  id               String            @id @default(cuid())
  loanId           String            @map("loan_id")
  installmentNo    Int               @map("installment_no")
  installmentDate  DateTime          @map("installment_date") @db.Date
  installmentAmount Decimal          @map("installment_amount") @db.Decimal(10, 2)
  advancePaid      Decimal?          @map("advance_paid") @db.Decimal(10, 2)
  due              Decimal?          @db.Decimal(10, 2)
  collectedBy      String?           @map("collected_by")
  collectionDate   DateTime?         @map("collection_date")
  status           InstallmentStatus @default(pending)
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")

  // Relations
  loan      Loan  @relation(fields: [loanId], references: [id])
  collector User? @relation("InstallmentCollector", fields: [collectedBy], references: [id])

  @@map("installments")
}

// 7. Branch Transactions Table
model BranchTransaction {
  id              String    @id @default(cuid())
  branchId        String    @map("branch_id")
  transactionType String    @map("transaction_type")
  createdBy       String    @map("created_by")
  entryType       EntryType @map("entry_type")
  serialNo        String?   @map("serial_no")
  date            DateTime  @db.Date
  description     String    @db.Text
  accountNo       String?   @map("account_no")
  category        String
  voucherNo       String?   @map("voucher_no")
  amount          Decimal   @db.Decimal(10, 2)
  enteredBy       String    @map("entered_by")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  branch    Branch @relation(fields: [branchId], references: [id])
  creator   User   @relation("TransactionCreator", fields: [createdBy], references: [id])
  enterer   User   @relation("TransactionEnterer", fields: [enteredBy], references: [id])

  @@map("branch_transactions")
}

// 8. Saving Accounts Table
model SavingAccount {
  id             String        @id @default(cuid())
  memberId       String        @map("member_id")
  savingType     SavingType    @map("saving_type")
  jointPhoto     String?       @db.Text @map("joint_photo")
  nomineeName    String?       @map("nominee_name")
  nomineeRelation String?      @map("nominee_relation")
  savingMethod   SavingMethod  @map("saving_method")
  monthlyAmount  Decimal?      @map("monthly_amount") @db.Decimal(10, 2)
  fdrAmount      Decimal?      @map("fdr_amount") @db.Decimal(10, 2)
  startDate      DateTime      @map("start_date") @db.Date
  createdBy      String        @map("created_by")
  isActive       Boolean       @default(true) @map("is_active")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")

  // Relations
  member  Member @relation(fields: [memberId], references: [id])
  creator User   @relation("SavingAccountCreator", fields: [createdBy], references: [id])

  @@map("saving_accounts")
}

// 9. Advertisements Table
model Advertisement {
  id           String    @id @default(cuid())
  title        String
  image        String?   @db.Text
  linkUrl      String?   @map("link_url") @db.Text
  isActive     Boolean   @default(true) @map("is_active")
  displayOrder Int?      @map("display_order")
  startDate    DateTime? @map("start_date") @db.Date
  endDate      DateTime? @map("end_date") @db.Date
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  @@map("advertisements")
}

// 10. User Sessions Table (for session management)
model UserSession {
  id          String    @id @default(cuid())
  userId      String    @map("user_id")
  sessionId   String    @unique @map("session_id")
  ipAddress   String    @map("ip_address")
  userAgent   String    @db.Text @map("user_agent")
  isActive    Boolean   @default(true) @map("is_active")
  rememberMe  Boolean   @default(false) @map("remember_me")
  expiresAt   DateTime  @map("expires_at")
  lastActivity DateTime @default(now()) @map("last_activity")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
  @@index([userId])
  @@index([sessionId])
  @@index([expiresAt])
}

// 11. Token Blacklist Table (for JWT invalidation)
model TokenBlacklist {
  id        String   @id @default(cuid())
  token     String   @unique @db.VarChar(500)
  tokenType String   @map("token_type") // 'access' or 'refresh'
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  @@map("token_blacklist")
  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

// 12. Login Attempts Table (for rate limiting)
model LoginAttempt {
  id          String    @id @default(cuid())
  userId      String?   @map("user_id")
  memberId    String?   @map("member_id")
  ipAddress   String    @map("ip_address")
  userAgent   String    @db.Text @map("user_agent")
  successful  Boolean   @default(false)
  failureReason String? @map("failure_reason")
  attemptedAt DateTime  @default(now()) @map("attempted_at")

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("login_attempts")
  @@index([userId])
  @@index([memberId])
  @@index([ipAddress])
  @@index([attemptedAt])
}

// 13. Password Reset Attempts Table (for rate limiting)
model PasswordResetAttempt {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  ipAddress   String   @map("ip_address")
  userAgent   String   @db.Text @map("user_agent")
  attemptedAt DateTime @default(now()) @map("attempted_at")

  @@map("password_reset_attempts")
  @@index([userId])
  @@index([ipAddress])
  @@index([attemptedAt])
}
