import { Router } from 'express';
import { authController } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import {
  validateRegister,
  validateLogin,
  validateUpdateProfile,
  validateChangePassword,
  handleValidationErrors,
} from '@/middleware/validation';

const router = Router();

// Public routes
router.post(
  '/register',
  validateRegister,
  handleValidationErrors,
  authController.register.bind(authController)
);

router.post(
  '/login',
  validateLogin,
  handleValidationErrors,
  authController.login.bind(authController)
);

router.post(
  '/refresh',
  authController.refreshToken.bind(authController)
);

// Protected routes
router.use(authenticate);

router.get(
  '/profile',
  authController.getProfile.bind(authController)
);

router.put(
  '/profile',
  validateUpdateProfile,
  handleValidationErrors,
  authController.updateProfile.bind(authController)
);

router.put(
  '/change-password',
  validateChangePassword,
  handleValidationErrors,
  authController.changePassword.bind(authController)
);

router.post(
  '/logout',
  authController.logout.bind(authController)
);

export default router;
