import { Router } from 'express';
import { authController } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import {
  validateRegister,
  validateLogin,
  validateUpdateProfile,
  validateChangePassword,
  validateForgotPassword,
  validateResetPassword,
  validateSessionId,
  handleValidationErrors,
} from '@/middleware/validation';
import {
  authRateLimit,
  passwordResetRateLimit,
} from '@/middleware/security';

const router = Router();

// Public routes
router.post(
  '/register',
  validateRegister,
  handleValidationErrors,
  authController.register.bind(authController)
);

router.post(
  '/login',
  authRateLimit,
  validateLogin,
  handleValidationErrors,
  authController.login.bind(authController)
);

router.post(
  '/refresh',
  authController.refreshToken.bind(authController)
);

router.post(
  '/forgot-password',
  passwordResetRateLimit,
  validateForgotPassword,
  handleValidationErrors,
  authController.forgotPassword.bind(authController)
);

router.post(
  '/reset-password',
  passwordResetRateLimit,
  validateResetPassword,
  handleValidationErrors,
  authController.resetPassword.bind(authController)
);

// Protected routes
router.use(authenticate);

router.get(
  '/profile',
  authController.getProfile.bind(authController)
);

router.put(
  '/profile',
  validateUpdateProfile,
  handleValidationErrors,
  authController.updateProfile.bind(authController)
);

router.put(
  '/change-password',
  validateChangePassword,
  handleValidationErrors,
  authController.changePassword.bind(authController)
);

router.post(
  '/logout',
  authController.logout.bind(authController)
);

router.post(
  '/logout-all',
  authController.logoutAll.bind(authController)
);

// Session management routes
router.get(
  '/sessions',
  authController.getActiveSessions.bind(authController)
);

router.delete(
  '/sessions/:sessionId',
  authController.revokeSession.bind(authController)
);

export default router;
