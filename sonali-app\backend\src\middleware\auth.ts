import { Request, Response, NextFunction } from 'express';
import { JwtUtil, JwtPayload } from '@/utils/jwt';
import { User, UserRole } from '@prisma/client';
import { prismaUserService } from '@/services/prisma/userService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      userId?: string;
    }
  }
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = JwtUtil.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      ResponseUtil.unauthorized(res, 'Access token is required');
      return;
    }

    const payload: JwtPayload = JwtUtil.verifyAccessToken(token);

    const user = await prismaUserService.findUserById(payload.userId);
    if (!user) {
      ResponseUtil.unauthorized(res, 'User not found');
      return;
    }

    if (!user.isActive) {
      ResponseUtil.unauthorized(res, 'Account is deactivated');
      return;
    }

    req.user = user;
    req.userId = user.id;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    ResponseUtil.unauthorized(res, 'Invalid or expired token');
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    if (!roles.includes(req.user.role)) {
      ResponseUtil.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = JwtUtil.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload: JwtPayload = JwtUtil.verifyAccessToken(token);
      const user = await prismaUserService.findUserById(payload.userId);

      if (user && user.isActive) {
        req.user = user;
        req.userId = user.id;
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return an error, just continue without user
    logger.debug('Optional auth failed:', error);
    next();
  }
};
