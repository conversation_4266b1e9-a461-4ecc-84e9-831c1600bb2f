import { prisma } from '@/config/database';
import { LoanApplication, Loan, LoanApplicationStatus, UserRole, RepaymentMethod, InstallmentStatus } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface CreateLoanApplicationData {
  memberId: string;
  appliedAmount: number;
  reason: string;
  loanCycleNumber: number;
  recommender: string;
  advancePayment?: number;
}

export interface UpdateLoanApplicationData {
  appliedAmount?: number;
  reason?: string;
  recommender?: string;
  advancePayment?: number;
}

export interface LoanApplicationListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: LoanApplicationStatus;
  branchId?: string;
  memberId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface LoanListQuery {
  page?: number;
  limit?: number;
  search?: string;
  branchId?: string;
  memberId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface LoanCalculationData {
  loanAmount: number;
  repaymentDuration: number;
  repaymentMethod: RepaymentMethod;
  advancePayment?: number;
  interestRate?: number;
}

export interface LoanCalculationResult {
  loanAmount: number;
  advancePayment: number;
  totalRepaymentAmount: number;
  installmentCount: number;
  installmentAmount: number;
  firstInstallmentDate: Date;
  lastInstallmentDate: Date;
  interestAmount: number;
  effectiveAmount: number;
}

export class LoanService {
  async getLoanApplications(query: LoanApplicationListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    applications: (LoanApplication & { member?: any; reviewer?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        branchId,
        memberId,
        sortBy = 'appliedAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.member = { branchId: userBranchId };
      } else if (branchId) {
        where.member = { branchId };
      }

      // Add member filter
      if (memberId) {
        where.memberId = memberId;
      }

      // Add status filter
      if (status) {
        where.status = status;
      }

      // Add search filter
      if (search) {
        where.OR = [
          { member: { name: { contains: search, mode: 'insensitive' } } },
          { member: { memberId: { contains: search, mode: 'insensitive' } } },
          { reason: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [applications, total] = await Promise.all([
        prisma.loanApplication.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            member: {
              select: {
                id: true,
                memberId: true,
                name: true,
                phoneNumber: true,
                branch: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            reviewer: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),
        prisma.loanApplication.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        applications,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get loan applications error:', error);
      throw error;
    }
  }

  async createLoanApplication(data: CreateLoanApplicationData): Promise<LoanApplication> {
    try {
      // Validate member exists and is active
      const member = await prisma.member.findUnique({
        where: { id: data.memberId },
        select: { id: true, isActive: true, name: true, memberId: true },
      });

      if (!member) {
        throw new Error('Member not found');
      }

      if (!member.isActive) {
        throw new Error('Member is not active');
      }

      // Check if member has any pending applications
      const pendingApplication = await prisma.loanApplication.findFirst({
        where: {
          memberId: data.memberId,
          status: LoanApplicationStatus.pending,
        },
      });

      if (pendingApplication) {
        throw new Error('Member already has a pending loan application');
      }

      const application = await prisma.loanApplication.create({
        data: {
          ...data,
          status: LoanApplicationStatus.pending,
          appliedAt: new Date(),
        },
        include: {
          member: {
            select: {
              id: true,
              memberId: true,
              name: true,
              phoneNumber: true,
            },
          },
        },
      });

      logger.info('Loan application created', {
        applicationId: application.id,
        memberId: application.memberId,
        amount: application.appliedAmount,
      });

      return application;
    } catch (error) {
      logger.error('Create loan application error:', error);
      throw error;
    }
  }

  async getLoanApplicationById(id: string, userRole: UserRole, userBranchId?: string): Promise<LoanApplication | null> {
    try {
      const application = await prisma.loanApplication.findUnique({
        where: { id },
        include: {
          member: {
            include: {
              branch: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          reviewer: {
            select: {
              id: true,
              name: true,
            },
          },
          loan: {
            select: {
              id: true,
              loanAmount: true,
              totalRepaymentAmount: true,
              repaymentDuration: true,
              loanDate: true,
            },
          },
        },
      });

      // Role-based access control
      if (application && userRole !== UserRole.admin && application.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view applications from your own branch');
      }

      return application;
    } catch (error) {
      logger.error('Get loan application by ID error:', error);
      throw error;
    }
  }

  async updateLoanApplication(id: string, data: UpdateLoanApplicationData, userRole: UserRole, userBranchId?: string): Promise<LoanApplication> {
    try {
      // Check if application exists and user has access
      const existingApplication = await prisma.loanApplication.findUnique({
        where: { id },
        include: {
          member: {
            select: {
              branchId: true,
            },
          },
        },
      });

      if (!existingApplication) {
        throw new Error('Loan application not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && existingApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only update applications from your own branch');
      }

      // Check if application can be updated
      if (existingApplication.status !== LoanApplicationStatus.pending) {
        throw new Error('Only pending applications can be updated');
      }

      const application = await prisma.loanApplication.update({
        where: { id },
        data,
        include: {
          member: {
            select: {
              id: true,
              memberId: true,
              name: true,
              phoneNumber: true,
            },
          },
        },
      });

      logger.info('Loan application updated', {
        applicationId: application.id,
        updatedFields: Object.keys(data),
      });

      return application;
    } catch (error) {
      logger.error('Update loan application error:', error);
      throw error;
    }
  }

  async approveLoanApplication(id: string, reviewerId: string): Promise<{ application: LoanApplication; loan: Loan }> {
    try {
      const application = await prisma.loanApplication.findUnique({
        where: { id },
        include: {
          member: true,
        },
      });

      if (!application) {
        throw new Error('Loan application not found');
      }

      if (application.status !== LoanApplicationStatus.pending) {
        throw new Error('Only pending applications can be approved');
      }

      // Calculate loan details
      const calculation = this.calculateLoan({
        loanAmount: application.appliedAmount,
        repaymentDuration: 12, // Default 12 months
        repaymentMethod: RepaymentMethod.monthly,
        advancePayment: application.advancePayment || 0,
      });

      // Update application status and create loan
      const [updatedApplication, loan] = await prisma.$transaction(async (tx) => {
        const updatedApp = await tx.loanApplication.update({
          where: { id },
          data: {
            status: LoanApplicationStatus.approved,
            reviewedBy: reviewerId,
            reviewedAt: new Date(),
          },
          include: {
            member: {
              select: {
                id: true,
                memberId: true,
                name: true,
              },
            },
          },
        });

        const newLoan = await tx.loan.create({
          data: {
            loanApplicationId: id,
            loanDate: new Date(),
            loanAmount: calculation.loanAmount,
            totalRepaymentAmount: calculation.totalRepaymentAmount,
            repaymentDuration: calculation.installmentCount,
            repaymentMethod: RepaymentMethod.monthly,
            installmentCount: calculation.installmentCount,
            installmentAmount: calculation.installmentAmount,
            advancePayment: calculation.advancePayment,
            firstInstallmentDate: calculation.firstInstallmentDate,
            lastInstallmentDate: calculation.lastInstallmentDate,
          },
        });

        // Create installments
        for (let i = 1; i <= calculation.installmentCount; i++) {
          const installmentDate = new Date(calculation.firstInstallmentDate);
          installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

          await tx.installment.create({
            data: {
              loanId: newLoan.id,
              installmentNo: i,
              installmentDate,
              installmentAmount: calculation.installmentAmount,
              status: InstallmentStatus.pending,
            },
          });
        }

        return [updatedApp, newLoan];
      });

      logger.info('Loan application approved', {
        applicationId: id,
        loanId: loan.id,
        amount: loan.loanAmount,
        reviewerId,
      });

      return { application: updatedApplication, loan };
    } catch (error) {
      logger.error('Approve loan application error:', error);
      throw error;
    }
  }

  async rejectLoanApplication(id: string, reviewerId: string, rejectionReason?: string): Promise<LoanApplication> {
    try {
      const application = await prisma.loanApplication.findUnique({
        where: { id },
      });

      if (!application) {
        throw new Error('Loan application not found');
      }

      if (application.status !== LoanApplicationStatus.pending) {
        throw new Error('Only pending applications can be rejected');
      }

      const updatedApplication = await prisma.loanApplication.update({
        where: { id },
        data: {
          status: LoanApplicationStatus.rejected,
          reviewedBy: reviewerId,
          reviewedAt: new Date(),
          // Note: You might want to add a rejectionReason field to the schema
        },
        include: {
          member: {
            select: {
              id: true,
              memberId: true,
              name: true,
            },
          },
        },
      });

      logger.info('Loan application rejected', {
        applicationId: id,
        reviewerId,
        reason: rejectionReason,
      });

      return updatedApplication;
    } catch (error) {
      logger.error('Reject loan application error:', error);
      throw error;
    }
  }

  async getLoans(query: LoanListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    loans: (Loan & { loanApplication?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        branchId,
        memberId,
        sortBy = 'loanDate',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.loanApplication = { member: { branchId: userBranchId } };
      } else if (branchId) {
        where.loanApplication = { member: { branchId } };
      }

      // Add member filter
      if (memberId) {
        where.loanApplication = { ...where.loanApplication, memberId };
      }

      // Add search filter
      if (search) {
        where.loanApplication = {
          ...where.loanApplication,
          member: {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { memberId: { contains: search, mode: 'insensitive' } },
            ],
          },
        };
      }

      const [loans, total] = await Promise.all([
        prisma.loan.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            loanApplication: {
              include: {
                member: {
                  select: {
                    id: true,
                    memberId: true,
                    name: true,
                    phoneNumber: true,
                    branch: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
        }),
        prisma.loan.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        loans,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get loans error:', error);
      throw error;
    }
  }

  async getLoanById(id: string, userRole: UserRole, userBranchId?: string): Promise<Loan | null> {
    try {
      const loan = await prisma.loan.findUnique({
        where: { id },
        include: {
          loanApplication: {
            include: {
              member: {
                include: {
                  branch: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
          installments: {
            orderBy: { installmentNo: 'asc' },
          },
        },
      });

      // Role-based access control
      if (loan && userRole !== UserRole.admin && loan.loanApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view loans from your own branch');
      }

      return loan;
    } catch (error) {
      logger.error('Get loan by ID error:', error);
      throw error;
    }
  }

  calculateLoan(data: LoanCalculationData): LoanCalculationResult {
    const {
      loanAmount,
      repaymentDuration,
      repaymentMethod,
      advancePayment = 0,
      interestRate = 10, // 10% default interest rate
    } = data;

    // Calculate interest amount
    const interestAmount = (loanAmount * interestRate) / 100;
    const totalRepaymentAmount = loanAmount + interestAmount;
    
    // Calculate installment count based on repayment method
    let installmentCount: number;
    switch (repaymentMethod) {
      case RepaymentMethod.weekly:
        installmentCount = repaymentDuration * 4; // 4 weeks per month
        break;
      case RepaymentMethod.monthly:
        installmentCount = repaymentDuration;
        break;
      case RepaymentMethod.quarterly:
        installmentCount = Math.ceil(repaymentDuration / 3);
        break;
      default:
        installmentCount = repaymentDuration;
    }

    // Calculate installment amount
    const installmentAmount = Math.round((totalRepaymentAmount / installmentCount) * 100) / 100;

    // Calculate dates
    const firstInstallmentDate = new Date();
    firstInstallmentDate.setDate(firstInstallmentDate.getDate() + 30); // First installment after 30 days

    const lastInstallmentDate = new Date(firstInstallmentDate);
    switch (repaymentMethod) {
      case RepaymentMethod.weekly:
        lastInstallmentDate.setDate(lastInstallmentDate.getDate() + (installmentCount - 1) * 7);
        break;
      case RepaymentMethod.monthly:
        lastInstallmentDate.setMonth(lastInstallmentDate.getMonth() + installmentCount - 1);
        break;
      case RepaymentMethod.quarterly:
        lastInstallmentDate.setMonth(lastInstallmentDate.getMonth() + (installmentCount - 1) * 3);
        break;
    }

    const effectiveAmount = loanAmount - advancePayment;

    return {
      loanAmount,
      advancePayment,
      totalRepaymentAmount,
      installmentCount,
      installmentAmount,
      firstInstallmentDate,
      lastInstallmentDate,
      interestAmount,
      effectiveAmount,
    };
  }

  async getLoanInstallments(loanId: string, userRole: UserRole, userBranchId?: string): Promise<any[]> {
    try {
      // Check if loan exists and user has access
      const loan = await prisma.loan.findUnique({
        where: { id: loanId },
        include: {
          loanApplication: {
            include: {
              member: {
                select: {
                  branchId: true,
                },
              },
            },
          },
        },
      });

      if (!loan) {
        throw new Error('Loan not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && loan.loanApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view installments from your own branch');
      }

      const installments = await prisma.installment.findMany({
        where: { loanId },
        orderBy: { installmentNo: 'asc' },
      });

      return installments;
    } catch (error) {
      logger.error('Get loan installments error:', error);
      throw error;
    }
  }
}

export const loanService = new LoanService();
