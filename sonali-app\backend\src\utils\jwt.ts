import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { UserAttributes } from '@/models/User';

export interface JwtPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

export class JwtUtil {
  static generateAccessToken(user: Partial<UserAttributes>): string {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: user.id!,
      email: user.email!,
      role: user.role!,
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
  }

  static generateRefreshToken(user: Partial<UserAttributes>): string {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: user.id!,
      email: user.email!,
      role: user.role!,
    };

    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
    });
  }

  static verifyAccessToken(token: string): JwtPayload {
    try {
      return jwt.verify(token, config.jwt.secret) as JwtPayload;
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  static verifyRefreshToken(token: string): JwtPayload {
    try {
      return jwt.verify(token, config.jwt.refreshSecret) as JwtPayload;
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  static generateTokenPair(user: Partial<UserAttributes>): {
    accessToken: string;
    refreshToken: string;
  } {
    return {
      accessToken: this.generateAccessToken(user),
      refreshToken: this.generateRefreshToken(user),
    };
  }

  static extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}
