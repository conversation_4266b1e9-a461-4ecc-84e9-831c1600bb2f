# Sonali App API Documentation

## Authentication Endpoints

### POST /api/auth/login
Login with Member ID or Email

**Request Body:**
```json
{
  "identifier": "MEM001", // Member ID or email
  "password": "password123",
  "rememberMe": false // Optional, default false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "memberId": "MEM001",
      "role": "member",
      "isActive": true
    },
    "accessToken": "jwt_access_token",
    "sessionId": "session_id",
    "expiresAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Login successful"
}
```

**Rate Limiting:** 5 attempts per 15 minutes per IP

### POST /api/auth/register
Register a new user

**Request Body:**
```json
{
  "name": "Full Name",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "member", // Optional, default "member"
  "memberId": "MEM001", // Optional
  "branchId": "branch_id" // Optional
}
```

### POST /api/auth/refresh
Refresh access token

**Request:** Uses HTTP-only cookie or body
```json
{
  "refreshToken": "refresh_token" // Optional if cookie is set
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_access_token"
  }
}
```

### POST /api/auth/logout
Logout current session

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### POST /api/auth/logout-all
Logout from all devices

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Logged out from all devices successfully"
}
```

### POST /api/auth/forgot-password
Initiate password reset

**Request Body:**
```json
{
  "identifier": "<EMAIL>" // Email or Member ID
}
```

**Response:**
```json
{
  "success": true,
  "message": "If an account with that identifier exists, a password reset link has been sent."
}
```

**Rate Limiting:** 3 attempts per hour per IP

### POST /api/auth/reset-password
Reset password with token

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password has been reset successfully. Please log in with your new password."
}
```

### GET /api/auth/profile
Get user profile

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "memberId": "MEM001",
    "role": "member",
    "isActive": true,
    "lastLoginAt": "2024-01-01T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /api/auth/profile
Update user profile

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>"
}
```

### PUT /api/auth/change-password
Change password

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
  "currentPassword": "currentpass",
  "newPassword": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

### GET /api/auth/sessions
Get active sessions

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "session_id",
      "sessionId": "session_identifier",
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0...",
      "isActive": true,
      "rememberMe": false,
      "lastActivity": "2024-01-01T00:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### DELETE /api/auth/sessions/:sessionId
Revoke a specific session

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Session revoked successfully"
}
```

## Error Responses

### Authentication Errors
```json
{
  "success": false,
  "error": "Invalid credentials"
}
```

### Rate Limiting Errors
```json
{
  "success": false,
  "error": "Too many login attempts. Please try again in 15 minutes."
}
```

### Validation Errors
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "password",
      "message": "Password must be at least 8 characters long"
    }
  ]
}
```

## Security Headers

All API responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy: default-src 'self'`

## Rate Limiting

Different endpoints have different rate limits:
- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 attempts per 15 minutes per IP
- **Password Reset**: 3 attempts per hour per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time

## Authentication Flow

1. **Login**: POST to `/api/auth/login` with credentials
2. **Store Token**: Save access token and use in Authorization header
3. **API Calls**: Include `Authorization: Bearer <token>` header
4. **Token Refresh**: Use refresh token when access token expires
5. **Logout**: POST to `/api/auth/logout` to invalidate session

## Security Best Practices

1. **HTTPS Only**: Always use HTTPS in production
2. **Token Storage**: Store tokens securely (HTTP-only cookies recommended)
3. **Token Expiration**: Handle token expiration gracefully
4. **Rate Limiting**: Respect rate limits and implement backoff
5. **Input Validation**: Validate all inputs on client side
6. **Error Handling**: Don't expose sensitive information in errors
