# Staging Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=staging

# Database Configuration
DB_HOST=your-staging-db-host
DB_PORT=3306
DB_NAME=sonali_app_staging
DB_USER=staging_user
DB_PASSWORD=your-staging-db-password

# JWT Configuration
JWT_SECRET=staging-super-secret-jwt-key-change-this
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=staging-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_SALT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=https://staging.sonali-app.com
