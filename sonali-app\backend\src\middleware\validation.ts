import { Request, Response, NextFunction } from 'express';
import { body, validationResult, Validation<PERSON>hain } from 'express-validator';
import { ResponseUtil } from '@/utils/response';

export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined,
    }));

    ResponseUtil.validationError(res, 'Validation failed');
    return;
  }

  next();
};

// Common validation rules
export const validateEmail = body('email')
  .isEmail()
  .withMessage('Please provide a valid email address')
  .normalizeEmail();

export const validatePassword = body('password')
  .isLength({ min: 8 })
  .withMessage('Password must be at least 8 characters long')
  .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');

export const validateName = (field: string) =>
  body(field)
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage(`${field} must be between 1 and 100 characters`)
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage(`${field} must contain only letters and spaces`);

// Auth validation chains
export const validateRegister: ValidationChain[] = [
  validateEmail,
  validatePassword,
  validateName('firstName'),
  validateName('lastName'),
];

export const validateLogin: ValidationChain[] = [
  validateEmail,
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

export const validateUpdateProfile: ValidationChain[] = [
  validateName('firstName').optional(),
  validateName('lastName').optional(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
];

export const validateChangePassword: ValidationChain[] = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    }),
];
