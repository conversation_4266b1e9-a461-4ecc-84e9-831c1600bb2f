import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class ResponseUtil {
  static success<T>(
    res: Response,
    data?: T,
    message?: string,
    statusCode: number = 200
  ): Response<ApiResponse<T>> {
    return res.status(statusCode).json({
      success: true,
      data,
      message,
    });
  }

  static error(
    res: Response,
    error: string,
    statusCode: number = 400,
    data?: any
  ): Response<ApiResponse> {
    return res.status(statusCode).json({
      success: false,
      error,
      data,
    });
  }

  static paginated<T>(
    res: Response,
    data: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    },
    message?: string,
    statusCode: number = 200
  ): Response<ApiResponse<T[]>> {
    return res.status(statusCode).json({
      success: true,
      data,
      message,
      pagination,
    });
  }

  static created<T>(
    res: Response,
    data?: T,
    message?: string
  ): Response<ApiResponse<T>> {
    return this.success(res, data, message, 201);
  }

  static noContent(res: Response): Response {
    return res.status(204).send();
  }

  static unauthorized(res: Response, message: string = 'Unauthorized'): Response<ApiResponse> {
    return this.error(res, message, 401);
  }

  static forbidden(res: Response, message: string = 'Forbidden'): Response<ApiResponse> {
    return this.error(res, message, 403);
  }

  static notFound(res: Response, message: string = 'Resource not found'): Response<ApiResponse> {
    return this.error(res, message, 404);
  }

  static conflict(res: Response, message: string = 'Conflict'): Response<ApiResponse> {
    return this.error(res, message, 409);
  }

  static validationError(res: Response, message: string = 'Validation failed'): Response<ApiResponse> {
    return this.error(res, message, 422);
  }

  static internalError(res: Response, message: string = 'Internal server error'): Response<ApiResponse> {
    return this.error(res, message, 500);
  }
}
