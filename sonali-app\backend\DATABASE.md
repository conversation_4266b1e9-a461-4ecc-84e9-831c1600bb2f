# Database Setup Guide

This document provides comprehensive information about the database setup for the Sonali App using Prisma ORM with MySQL.

## 📊 Database Schema Overview

The Sonali App uses a comprehensive database schema designed for microfinance operations:

### Core Tables

1. **Branches** - Branch management
2. **Users** - System users (admin, manager, field_officer, member)
3. **Members** - Microfinance members/customers
4. **Loan Applications** - Loan application requests
5. **Loans** - Approved loans
6. **Installments** - Loan repayment installments
7. **Branch Transactions** - Financial transactions
8. **Saving Accounts** - Member savings accounts
9. **Advertisements** - System advertisements

### Key Relationships

- Users belong to Branches
- Members belong to Branches and are created by Users
- Loan Applications are submitted by Members
- Loans are created from approved Loan Applications
- Installments belong to Loans
- Saving Accounts belong to Members

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 20 LTS
- MySQL 8.x
- npm or yarn

### Quick Setup

1. **Configure Environment**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your MySQL credentials
   ```

2. **Run Setup Script**
   
   **Linux/macOS:**
   ```bash
   ./scripts/setup-database.sh
   ```
   
   **Windows:**
   ```cmd
   scripts\setup-database.bat
   ```

### Manual Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Database URL**
   ```env
   DATABASE_URL="mysql://username:password@localhost:3306/sonali_app"
   ```

3. **Generate Prisma Client**
   ```bash
   npm run db:generate
   ```

4. **Run Migrations**
   ```bash
   npm run db:migrate
   ```

5. **Seed Database**
   ```bash
   npm run db:seed
   ```

## 🔧 Available Commands

```bash
# Generate Prisma client
npm run db:generate

# Create and run new migration
npm run db:migrate

# Deploy migrations (production)
npm run db:migrate:prod

# Seed database with sample data
npm run db:seed

# Open Prisma Studio (Database GUI)
npm run db:studio

# Reset database (WARNING: Deletes all data)
npm run db:reset
```

## 👥 Default Users

After seeding, the following users are available:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | admin123 | System administrator |
| Manager | <EMAIL> | manager123 | Branch manager |
| Field Officer | <EMAIL> | officer123 | Field operations |

## 📋 Sample Data

The seed script creates:

- 1 Main Branch
- 3 System Users (Admin, Manager, Field Officer)
- 2 Sample Members
- 1 Approved Loan with Installments
- 2 Saving Accounts
- Sample Transactions
- 2 Advertisements

## 🔍 Database Schema Details

### User Roles

- **admin**: Full system access
- **manager**: Branch management and oversight
- **field_officer**: Member and loan management
- **member**: Limited access for member portal

### Loan Workflow

1. Member submits Loan Application
2. Manager/Admin reviews and approves
3. Loan is created with installment schedule
4. Field Officer collects installments
5. Transactions are recorded

### Saving Types

- **general**: Regular savings account
- **dps**: Deposit Pension Scheme
- **fdr**: Fixed Deposit Receipt

## 🚀 Production Deployment

### Migration Strategy

1. **Backup existing database**
2. **Run migrations**
   ```bash
   npm run db:migrate:prod
   ```
3. **Verify data integrity**

### Environment Variables

```env
# Production Database URL
DATABASE_URL="mysql://user:password@host:port/database"

# Connection pooling (recommended)
DATABASE_URL="mysql://user:password@host:port/database?connection_limit=10&pool_timeout=20"
```

## 🔒 Security Considerations

- Use strong database passwords
- Enable SSL for database connections in production
- Regularly backup your database
- Monitor database performance and logs
- Use connection pooling for better performance

## 📊 Monitoring

### Health Checks

The application includes database health checks:

```typescript
// Check database connectivity
GET /health
```

### Performance Monitoring

- Monitor slow queries
- Track connection pool usage
- Set up alerts for database errors

## 🛠️ Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if MySQL is running
   - Verify connection credentials
   - Check firewall settings

2. **Migration Errors**
   - Ensure database exists
   - Check user permissions
   - Review migration files

3. **Seed Errors**
   - Run migrations first
   - Check for existing data conflicts
   - Verify foreign key constraints

### Debug Mode

Enable Prisma query logging:

```env
# In .env
DATABASE_URL="mysql://user:password@host:port/database?connection_limit=10&pool_timeout=20"
```

Add to Prisma client:
```typescript
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});
```

## 📚 Additional Resources

- [Prisma Documentation](https://www.prisma.io/docs/)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Database Design Best Practices](https://www.prisma.io/docs/guides/database/developing-with-prisma-migrate)

## 🆘 Support

For database-related issues:

1. Check the logs in `logs/` directory
2. Review Prisma error messages
3. Consult the troubleshooting section above
4. Open an issue in the project repository
