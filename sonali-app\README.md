# Sonali App

A modern full-stack web application built with React 19, TypeScript, Node.js, Express, and MySQL.

## 🚀 Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **React Router** for navigation
- **TanStack Query** for data fetching
- **Zustand** for state management
- **React Hook Form** with Zod validation

### Backend
- **Node.js 20 LTS** with TypeScript
- **Express.js** web framework
- **MySQL 8.x** database
- **Prisma ORM** with TypeScript
- **JWT** authentication
- **bcryptjs** for password hashing
- **Express Rate Limit** for API protection

### DevOps
- **Docker** and **Docker Compose** for containerization
- **Nginx** for production web server
- **GitHub Actions** ready for CI/CD
- **VPS deployment** scripts included

## 📁 Project Structure

```
sonali-app/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # CSS and styling files
│   ├── public/             # Static assets
│   └── package.json
├── backend/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Request handlers
│   │   ├── models/         # Database models
│   │   ├── routes/         # API route definitions
│   │   ├── middleware/     # Express middleware
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── config/         # Configuration files
│   └── package.json
├── shared/                  # Shared TypeScript types
│   └── types/
├── deploy/                  # Deployment configurations
└── docker-compose.yml       # Development environment
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 20 LTS
- MySQL 8.x
- Docker and Docker Compose (optional)

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sonali-app
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/api
   - phpMyAdmin: http://localhost:8080

### Manual Setup

1. **Database Setup**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your database credentials

   # Run setup script (Linux/macOS)
   ./scripts/setup-database.sh

   # Or on Windows
   scripts\setup-database.bat
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   npm run dev
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   cp .env.example .env
   npm run dev
   ```

## 🚀 Deployment

### VPS Deployment

The project includes automated deployment scripts for VPS deployment.

1. **Configure environment**
   ```bash
   cd deploy
   cp .env.production .env
   # Edit .env with your production values
   ```

2. **Deploy to VPS**
   ```bash
   ./deploy.sh production
   ```

### Environment Variables

#### Backend (.env)
```env
# Server
PORT=3001
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app
DB_USER=root
DB_PASSWORD=

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
```

#### Frontend (.env)
```env
# API
VITE_API_URL=http://localhost:3001/api

# App
VITE_APP_NAME=Sonali App
VITE_APP_VERSION=1.0.0
```

## 📚 API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/change-password` - Change password
- `POST /api/auth/logout` - User logout

### Health Check

- `GET /health` - Server health check

## 🧪 Testing

```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

## 🔧 Available Scripts

### Backend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests

## 🛡️ Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- SQL injection prevention (Sequelize ORM)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.

---

Built with ❤️ by the Sonali App Team
