# Sonali App

A modern full-stack web application built with React 19, TypeScript, Node.js, Express, and MySQL.

## 🚀 Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **React Router** for navigation
- **TanStack Query** for data fetching
- **Zustand** for state management
- **React Hook Form** with Zod validation

### Backend
- **Node.js 20 LTS** with TypeScript
- **Express.js** web framework
- **MySQL 8.x** database
- **Prisma ORM** with TypeScript
- **JWT** authentication
- **bcryptjs** for password hashing
- **Express Rate Limit** for API protection

### DevOps
- **Docker** and **Docker Compose** for containerization
- **Nginx** for production web server
- **GitHub Actions** ready for CI/CD
- **VPS deployment** scripts included

## 📁 Project Structure

```
sonali-app/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # CSS and styling files
│   ├── public/             # Static assets
│   └── package.json
├── backend/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Request handlers
│   │   ├── models/         # Database models
│   │   ├── routes/         # API route definitions
│   │   ├── middleware/     # Express middleware
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── config/         # Configuration files
│   └── package.json
├── shared/                  # Shared TypeScript types
│   └── types/
├── deploy/                  # Deployment configurations
└── docker-compose.yml       # Development environment
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 20 LTS
- MySQL 8.x
- Docker and Docker Compose (optional)

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sonali-app
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/api
   - phpMyAdmin: http://localhost:8080

### Manual Setup

1. **Database Setup**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your database credentials

   # Run setup script (Linux/macOS)
   ./scripts/setup-database.sh

   # Or on Windows
   scripts\setup-database.bat
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   npm run dev
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   cp .env.example .env
   npm run dev
   ```

## 🚀 Deployment

### VPS Deployment

The project includes automated deployment scripts for VPS deployment.

1. **Configure environment**
   ```bash
   cd deploy
   cp .env.production .env
   # Edit .env with your production values
   ```

2. **Deploy to VPS**
   ```bash
   ./deploy.sh production
   ```

### Environment Variables

#### Backend (.env)
```env
# Server
PORT=3001
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app
DB_USER=root
DB_PASSWORD=

# Prisma Database URL
DATABASE_URL="mysql://root:@localhost:3306/sonali_app"

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
```

#### Frontend (.env)
```env
# API
VITE_API_URL=http://localhost:3001/api

# App
VITE_APP_NAME=Sonali App
VITE_APP_VERSION=1.0.0
```

## 📚 API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login (Member ID or email)
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout current session
- `POST /api/auth/logout-all` - Logout from all devices
- `POST /api/auth/forgot-password` - Initiate password reset
- `POST /api/auth/reset-password` - Reset password with token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/change-password` - Change password
- `GET /api/auth/sessions` - Get active sessions
- `DELETE /api/auth/sessions/:id` - Revoke specific session

### Health Check

- `GET /health` - Server health check

For detailed API documentation, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

## 🧪 Testing

### Security Testing
```bash
# Run comprehensive security tests
cd backend/scripts
./test-security.sh        # Linux/macOS
test-security.bat         # Windows
```

### Unit Testing
```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

### Manual Testing
- Test Member ID and email login
- Verify rate limiting functionality
- Test password reset flow
- Check session management
- Validate security headers

## 🔧 Available Scripts

### Backend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests

## 🛡️ Security Features

### Authentication & Authorization
- **JWT-based authentication** with refresh token rotation
- **Member ID based login** (email or Member ID)
- **Password hashing** with bcryptjs (configurable salt rounds)
- **Role-based access control** middleware
- **Remember me functionality** with extended sessions
- **Session management** with concurrent session limits
- **Token blacklisting** and version control

### Security Protection
- **Rate limiting** for login attempts with progressive lockout
- **Password reset** functionality with secure tokens
- **CORS configuration** for www.sonalibd.org domain
- **Input validation** and sanitization (DOMPurify)
- **SQL injection prevention** with pattern detection
- **XSS protection** with comprehensive filtering
- **Security headers** (Helmet.js, CSP, HSTS)
- **Request size validation** and monitoring

### Frontend Security
- **Code obfuscation** with Webpack/Vite configuration
- **Console log removal** in production builds
- **Source map protection** (disabled in production)
- **Asset name randomization** for security
- **Bundle optimization** and tree shaking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.

---

Built with ❤️ by the Sonali App Team
