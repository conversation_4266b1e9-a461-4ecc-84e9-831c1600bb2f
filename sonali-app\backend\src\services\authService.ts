import { User, UserRole } from '@prisma/client';
import { prismaUserService } from '@/services/prisma/userService';
import { sessionService } from '@/services/sessionService';
import { rateLimitService } from '@/services/rateLimitService';
import { JwtUtil } from '@/utils/jwt';
import { logger } from '@/utils/logger';

export interface LoginCredentials {
  identifier: string; // Can be email or member ID
  password: string;
  rememberMe?: boolean;
  ipAddress?: string;
  userAgent?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  accessToken: string;
  refreshToken: string;
  sessionId: string;
  expiresAt: Date;
}

export class AuthService {
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Create new user
      const user = await prismaUserService.createUser({
        email: data.email,
        password: data.password,
        name: data.name,
        role: data.role || UserRole.member,
        memberId: data.memberId,
        branchId: data.branchId,
      });

      // Generate tokens
      const { accessToken, refreshToken } = JwtUtil.generateTokenPair(user);

      logger.info('User registered successfully', { userId: user.id, email: user.email });

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { identifier, password, rememberMe = false, ipAddress = '', userAgent = '' } = credentials;

      // Check rate limiting first
      const rateLimitResult = await rateLimitService.checkRateLimit(ipAddress);
      if (!rateLimitResult.allowed) {
        await rateLimitService.recordLoginAttempt({
          memberId: identifier,
          ipAddress,
          userAgent,
          successful: false,
          failureReason: 'Rate limit exceeded',
        });
        throw new Error(`Too many login attempts. Please try again in ${rateLimitResult.lockoutDuration} minutes.`);
      }

      // Find user by email or member ID
      const user = await prismaUserService.findUserByEmailOrMemberId(identifier);
      if (!user) {
        await rateLimitService.recordLoginAttempt({
          memberId: identifier,
          ipAddress,
          userAgent,
          successful: false,
          failureReason: 'User not found',
        });
        throw new Error('Invalid credentials');
      }

      // Check user-specific rate limiting
      const userRateLimitResult = await rateLimitService.checkRateLimit(ipAddress, user.id);
      if (!userRateLimitResult.allowed) {
        await rateLimitService.recordLoginAttempt({
          userId: user.id,
          memberId: identifier,
          ipAddress,
          userAgent,
          successful: false,
          failureReason: 'User rate limit exceeded',
        });
        throw new Error(`Account temporarily locked. Please try again in ${userRateLimitResult.lockoutDuration} minutes.`);
      }

      // Check if user is active
      if (!user.isActive) {
        await rateLimitService.recordLoginAttempt({
          userId: user.id,
          memberId: identifier,
          ipAddress,
          userAgent,
          successful: false,
          failureReason: 'Account deactivated',
        });
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await prismaUserService.comparePassword(user, password);
      if (!isPasswordValid) {
        await rateLimitService.recordLoginAttempt({
          userId: user.id,
          memberId: identifier,
          ipAddress,
          userAgent,
          successful: false,
          failureReason: 'Invalid password',
        });
        throw new Error('Invalid credentials');
      }

      // Generate session and tokens
      const { accessToken, refreshToken, sessionId } = JwtUtil.generateTokenPair(user);

      // Create session
      const session = await sessionService.createSession({
        userId: user.id,
        sessionId,
        ipAddress,
        userAgent,
        rememberMe,
      });

      // Enforce concurrent session limit
      await sessionService.enforceConcurrentSessionLimit(user.id, 5);

      // Update last login and reset failed attempts
      await prismaUserService.updateLastLogin(user.id);
      await rateLimitService.resetUserFailedAttempts(user.id);

      // Record successful login
      await rateLimitService.recordLoginAttempt({
        userId: user.id,
        memberId: identifier,
        ipAddress,
        userAgent,
        successful: true,
      });

      logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email,
        memberId: user.memberId,
        sessionId,
        rememberMe
      });

      return {
        user,
        accessToken,
        refreshToken,
        sessionId,
        expiresAt: session.expiresAt,
      };
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify refresh token
      const payload = await JwtUtil.verifyRefreshToken(refreshToken);

      // Find user
      const user = await prismaUserService.findUserById(payload.userId);
      if (!user || !user.isActive) {
        throw new Error('Invalid refresh token');
      }

      // Check token version
      if (payload.tokenVersion !== user.tokenVersion) {
        throw new Error('Token has been invalidated');
      }

      // Validate session
      if (payload.sessionId) {
        const isSessionValid = await sessionService.isSessionValid(payload.sessionId);
        if (!isSessionValid) {
          throw new Error('Session has expired');
        }
      }

      // Invalidate old refresh token
      await JwtUtil.invalidateToken(refreshToken, 'refresh', user.id);

      // Generate new tokens with same session ID
      const tokens = JwtUtil.generateTokenPair(user, payload.sessionId);

      logger.info('Token refreshed successfully', { userId: user.id, sessionId: payload.sessionId });

      return tokens;
    } catch (error) {
      logger.error('Token refresh error:', error);
      throw new Error('Invalid refresh token');
    }
  }

  async invalidateAllUserTokens(userId: string): Promise<void> {
    try {
      await prismaUserService.updateUser(userId, {
        // This will increment the token version, invalidating all existing tokens
      });

      logger.info('All user tokens invalidated', { userId });
    } catch (error) {
      logger.error('Error invalidating user tokens:', error);
      throw error;
    }
  }

  async getProfile(userId: string): Promise<User> {
    try {
      const user = await prismaUserService.findUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      return user;
    } catch (error) {
      logger.error('Get profile error:', error);
      throw error;
    }
  }

  async updateProfile(
    userId: string,
    data: Partial<Pick<User, 'name' | 'email'>>
  ): Promise<User> {
    try {
      const updatedUser = await prismaUserService.updateUser(userId, data);

      logger.info('Profile updated successfully', { userId: updatedUser.id });

      return updatedUser;
    } catch (error) {
      logger.error('Update profile error:', error);
      throw error;
    }
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      const user = await prismaUserService.findUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await prismaUserService.comparePassword(user, currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      await prismaUserService.changePassword(userId, newPassword);

      logger.info('Password changed successfully', { userId: user.id });
    } catch (error) {
      logger.error('Change password error:', error);
      throw error;
    }
  }
}

export const authService = new AuthService();
