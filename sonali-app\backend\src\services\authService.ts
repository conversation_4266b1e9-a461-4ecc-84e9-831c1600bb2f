import { User, UserRole } from '@prisma/client';
import { prismaUserService } from '@/services/prisma/userService';
import { JwtUtil } from '@/utils/jwt';
import { logger } from '@/utils/logger';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  accessToken: string;
  refreshToken: string;
}

export class AuthService {
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Create new user
      const user = await prismaUserService.createUser({
        email: data.email,
        password: data.password,
        name: data.name,
        role: data.role || UserRole.member,
        memberId: data.memberId,
        branchId: data.branchId,
      });

      // Generate tokens
      const { accessToken, refreshToken } = JwtUtil.generateTokenPair(user);

      logger.info('User registered successfully', { userId: user.id, email: user.email });

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Find user by email
      const user = await prismaUserService.findUserByEmail(credentials.email);
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await prismaUserService.comparePassword(user, credentials.password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Update last login
      await prismaUserService.updateLastLogin(user.id);

      // Generate tokens
      const { accessToken, refreshToken } = JwtUtil.generateTokenPair(user);

      logger.info('User logged in successfully', { userId: user.id, email: user.email });

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify refresh token
      const payload = JwtUtil.verifyRefreshToken(refreshToken);

      // Find user
      const user = await prismaUserService.findUserById(payload.userId);
      if (!user || !user.isActive) {
        throw new Error('Invalid refresh token');
      }

      // Generate new tokens
      const tokens = JwtUtil.generateTokenPair(user);

      logger.info('Token refreshed successfully', { userId: user.id });

      return tokens;
    } catch (error) {
      logger.error('Token refresh error:', error);
      throw new Error('Invalid refresh token');
    }
  }

  async getProfile(userId: string): Promise<User> {
    try {
      const user = await prismaUserService.findUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      return user;
    } catch (error) {
      logger.error('Get profile error:', error);
      throw error;
    }
  }

  async updateProfile(
    userId: string,
    data: Partial<Pick<User, 'name' | 'email'>>
  ): Promise<User> {
    try {
      const updatedUser = await prismaUserService.updateUser(userId, data);

      logger.info('Profile updated successfully', { userId: updatedUser.id });

      return updatedUser;
    } catch (error) {
      logger.error('Update profile error:', error);
      throw error;
    }
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      const user = await prismaUserService.findUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await prismaUserService.comparePassword(user, currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      await prismaUserService.changePassword(userId, newPassword);

      logger.info('Password changed successfully', { userId: user.id });
    } catch (error) {
      logger.error('Change password error:', error);
      throw error;
    }
  }
}

export const authService = new AuthService();
