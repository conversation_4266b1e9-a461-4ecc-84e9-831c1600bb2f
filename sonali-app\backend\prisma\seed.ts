import { PrismaClient, UserRole, SavingType, SavingMethod, LoanApplicationStatus, RepaymentMethod, InstallmentStatus, EntryType } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create main branch
  const mainBranch = await prisma.branch.create({
    data: {
      name: 'Main Branch',
      address: 'Dhaka, Bangladesh',
      isActive: true,
    },
  });

  console.log('✅ Created main branch');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);
  const adminUser = await prisma.user.create({
    data: {
      name: 'System Administrator',
      email: '<EMAIL>',
      password: hashedPassword,
      role: UserRole.admin,
      memberId: 'ADMIN001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  console.log('✅ Created admin user');

  // Create branch manager
  const managerPassword = await bcrypt.hash('manager123', 12);
  const managerUser = await prisma.user.create({
    data: {
      name: 'Branch Manager',
      email: '<EMAIL>',
      password: managerPassword,
      role: UserRole.manager,
      memberId: 'MGR001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  // Update branch with manager
  await prisma.branch.update({
    where: { id: mainBranch.id },
    data: { managerId: managerUser.id },
  });

  console.log('✅ Created branch manager');

  // Create field officer
  const officerPassword = await bcrypt.hash('officer123', 12);
  const fieldOfficer = await prisma.user.create({
    data: {
      name: 'Field Officer',
      email: '<EMAIL>',
      password: officerPassword,
      role: UserRole.field_officer,
      memberId: 'OFF001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  console.log('✅ Created field officer');

  // Create sample members
  const member1 = await prisma.member.create({
    data: {
      memberId: 'MEM001',
      name: 'Fatima Begum',
      fatherOrHusbandName: 'Abdul Rahman',
      motherName: 'Rashida Begum',
      presentAddress: 'House 123, Road 5, Dhanmondi, Dhaka',
      permanentAddress: 'Village: Savar, Upazila: Savar, District: Dhaka',
      nidNumber: '1234567890123',
      dateOfBirth: new Date('1985-05-15'),
      religion: 'Islam',
      phoneNumber: '+8801712345678',
      bloodGroup: 'B+',
      occupation: 'Small Business',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  const member2 = await prisma.member.create({
    data: {
      memberId: 'MEM002',
      name: 'Rashida Khatun',
      fatherOrHusbandName: 'Mohammad Ali',
      motherName: 'Salma Begum',
      presentAddress: 'House 456, Road 7, Uttara, Dhaka',
      permanentAddress: 'Village: Manikganj, Upazila: Manikganj, District: Manikganj',
      nidNumber: '9876543210987',
      dateOfBirth: new Date('1990-08-20'),
      religion: 'Islam',
      phoneNumber: '+8801987654321',
      bloodGroup: 'A+',
      occupation: 'Tailoring',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
      referenceId: member1.id,
    },
  });

  console.log('✅ Created sample members');

  // Create sample loan application
  const loanApplication = await prisma.loanApplication.create({
    data: {
      memberId: member1.id,
      appliedAmount: 50000,
      reason: 'Small business expansion - buying inventory for grocery shop',
      loanCycleNumber: 1,
      recommender: 'Field Officer',
      advancePayment: 5000,
      status: LoanApplicationStatus.approved,
      reviewedBy: managerUser.id,
      reviewedAt: new Date(),
      appliedAt: new Date(),
    },
  });

  // Create approved loan
  const loan = await prisma.loan.create({
    data: {
      loanApplicationId: loanApplication.id,
      loanDate: new Date(),
      loanAmount: 50000,
      totalRepaymentAmount: 55000,
      repaymentDuration: 12,
      repaymentMethod: RepaymentMethod.monthly,
      installmentCount: 12,
      installmentAmount: 4583.33,
      advancePayment: 5000,
      firstInstallmentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      lastInstallmentDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  // Create installments for the loan
  for (let i = 1; i <= 12; i++) {
    const installmentDate = new Date(Date.now() + (i * 30 * 24 * 60 * 60 * 1000)); // Monthly installments
    await prisma.installment.create({
      data: {
        loanId: loan.id,
        installmentNo: i,
        installmentDate,
        installmentAmount: 4583.33,
        status: i === 1 ? InstallmentStatus.paid : InstallmentStatus.pending,
        collectedBy: i === 1 ? fieldOfficer.id : null,
        collectionDate: i === 1 ? new Date() : null,
      },
    });
  }

  console.log('✅ Created sample loan and installments');

  // Create saving accounts
  await prisma.savingAccount.create({
    data: {
      memberId: member1.id,
      savingType: SavingType.general,
      nomineeName: 'Abdul Rahman',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.monthly,
      monthlyAmount: 1000,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  await prisma.savingAccount.create({
    data: {
      memberId: member2.id,
      savingType: SavingType.dps,
      nomineeName: 'Mohammad Ali',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.daily,
      monthlyAmount: 500,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  console.log('✅ Created saving accounts');

  // Create sample branch transactions
  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Loan Disbursement',
      createdBy: managerUser.id,
      entryType: EntryType.expense,
      serialNo: 'TXN001',
      date: new Date(),
      description: 'Loan disbursement to Fatima Begum',
      accountNo: 'ACC001',
      category: 'Loan',
      voucherNo: 'VOU001',
      amount: 45000, // 50000 - 5000 advance
      enteredBy: fieldOfficer.id,
    },
  });

  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Installment Collection',
      createdBy: fieldOfficer.id,
      entryType: EntryType.income,
      serialNo: 'TXN002',
      date: new Date(),
      description: 'First installment collection from Fatima Begum',
      accountNo: 'ACC001',
      category: 'Loan Repayment',
      voucherNo: 'VOU002',
      amount: 4583.33,
      enteredBy: fieldOfficer.id,
    },
  });

  console.log('✅ Created sample transactions');

  // Create sample advertisements
  await prisma.advertisement.create({
    data: {
      title: 'Welcome to Sonali Microfinance',
      image: 'https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=Welcome+to+Sonali',
      linkUrl: '/about',
      isActive: true,
      displayOrder: 1,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'Apply for Micro Loans Today',
      image: 'https://via.placeholder.com/800x400/059669/FFFFFF?text=Apply+for+Loans',
      linkUrl: '/loans/apply',
      isActive: true,
      displayOrder: 2,
      startDate: new Date(),
      endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
    },
  });

  console.log('✅ Created sample advertisements');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Default Login Credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Manager: <EMAIL> / manager123');
  console.log('Field Officer: <EMAIL> / officer123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
