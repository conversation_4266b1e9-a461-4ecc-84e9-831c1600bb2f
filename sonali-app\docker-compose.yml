version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: sonali-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: sonali_app
      MYSQL_USER: sonali_user
      MY<PERSON>QL_PASSWORD: sonali_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - sonali-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sonali-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: sonali_app
      DB_USER: sonali_user
      DB_PASSWORD: sonali_password
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_SECRET: your-refresh-secret-key
      JWT_REFRESH_EXPIRES_IN: 30d
      BCRYPT_SALT_ROUNDS: 12
      CORS_ORIGIN: http://localhost:5173
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - sonali-network
    command: npm run dev

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: sonali-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:3001/api
      VITE_APP_NAME: Sonali App
      VITE_APP_VERSION: 1.0.0
      VITE_NODE_ENV: development
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - sonali-network
    command: npm run dev

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: sonali-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    ports:
      - "8080:80"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - sonali-network

volumes:
  mysql_data:
    driver: local

networks:
  sonali-network:
    driver: bridge
