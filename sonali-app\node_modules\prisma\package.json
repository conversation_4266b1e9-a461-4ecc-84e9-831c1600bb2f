{"version": "6.11.1", "name": "prisma", "description": "Prisma is an open-source database toolkit. It includes a JavaScript/TypeScript ORM for Node.js, migrations and a modern GUI to view and edit the data in your database. You can use Prisma in new projects or add it to an existing one.", "keywords": ["CLI", "ORM", "Prisma", "Prisma CLI", "prisma2", "database", "db", "JavaScript", "JS", "TypeScript", "TS", "SQL", "SQLite", "pg", "Postgres", "PostgreSQL", "CockroachDB", "MySQL", "MariaDB", "MSSQL", "SQL Server", "SQLServer", "MongoDB"], "main": "build/index.js", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/cli"}, "homepage": "https://www.prisma.io", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "prisma": {"prismaCommit": "fd4e7ad5f8b6de45de783143061eb82fd5bf24c7"}, "files": ["README.md", "build", "config.js", "config.d.ts", "dist/cli/src/types.d.ts", "install", "runtime/*.js", "runtime/*.d.ts", "runtime/utils", "runtime/dist", "runtime/llhttp", "prisma-client", "preinstall", "scripts/preinstall-entry.js"], "pkg": {"assets": ["build/**/*", "runtime/**/*", "prisma-client/**/*", "node_modules/@prisma/engines/**/*", "node_modules/@prisma/engines/*"]}, "bin": {"prisma": "build/index.js"}, "types": "./dist/cli/src/types.d.ts", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./dist/cli/src/types.d.ts", "default": "./build/types.js"}, "import": {"types": "./dist/cli/src/types.d.ts", "default": "./build/types.js"}, "default": "./build/types.js"}, "./config": {"require": {"types": "./config.d.ts", "default": "./config.js"}, "import": {"types": "./config.d.ts", "default": "./config.js"}, "default": "./config.js"}, "./build/index.js": {"require": {"types": "./dist/cli/src/types.d.ts", "default": "./build/index.js"}, "default": "./build/index.js"}}, "devDependencies": {"@inquirer/prompts": "7.3.3", "@libsql/client": "0.8.1", "@modelcontextprotocol/sdk": "1.13.2", "@prisma/mini-proxy": "0.9.5", "@prisma/studio": "0.511.0", "@prisma/studio-server": "0.511.0", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/debug": "4.1.12", "@types/fs-extra": "11.0.4", "@types/jest": "29.5.14", "@types/node": "18.19.76", "@types/rimraf": "4.0.5", "async-listen": "3.1.0", "checkpoint-client": "1.1.33", "chokidar": "4.0.3", "debug": "4.4.0", "dotenv": "16.5.0", "effect": "3.12.10", "env-paths": "2.2.1", "esbuild": "0.25.1", "execa": "5.1.1", "fast-glob": "3.3.3", "fs-extra": "11.3.0", "fs-jetpack": "5.1.0", "get-port": "5.1.1", "get-tsconfig": "4.10.0", "global-dirs": "4.0.0", "jest": "29.7.0", "jest-junit": "16.0.0", "kleur": "4.1.5", "line-replace": "2.0.1", "log-update": "6.1.0", "node-fetch": "3.3.2", "npm-packlist": "5.1.3", "open": "7.4.2", "ora": "8.2.0", "pkg-up": "3.1.0", "resolve-pkg": "2.0.0", "rimraf": "6.0.1", "strip-ansi": "6.0.1", "ts-pattern": "5.6.2", "typescript": "5.4.5", "vitest": "3.1.3", "xdg-app-paths": "8.3.0", "zod": "3.24.2", "zx": "8.4.1", "@prisma/adapter-libsql": "6.11.1", "@prisma/client-generator-registry": "6.11.1", "@prisma/debug": "6.11.1", "@prisma/dmmf": "6.11.1", "@prisma/fetch-engine": "6.11.1", "@prisma/client": "6.11.1", "@prisma/generator": "6.11.1", "@prisma/get-platform": "6.11.1", "@prisma/internals": "6.11.1", "@prisma/migrate": "6.11.1"}, "dependencies": {"@prisma/config": "6.11.1", "@prisma/engines": "6.11.1"}, "peerDependencies": {"typescript": ">=5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "sideEffects": false, "scripts": {"prisma": "tsx src/bin.ts", "platform": "tsx src/bin.ts platform --early-access", "pm": "tsx src/bin.ts platform --early-access", "dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "dotenv -e ../../.db.env -- tsx helpers/run-tests.ts", "test:platform": "dotenv -e ../../.db.env -- tsx helpers/run-tests.ts src/platform", "tsc": "tsc -d -p tsconfig.build.json", "preinstall": "node scripts/preinstall-entry.js"}}