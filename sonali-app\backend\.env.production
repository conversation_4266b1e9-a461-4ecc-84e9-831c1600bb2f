# Production Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration
DB_HOST=your-production-db-host
DB_PORT=3306
DB_NAME=sonali_app_production
DB_USER=production_user
DB_PASSWORD=your-production-db-password

# JWT Configuration
JWT_SECRET=production-super-secret-jwt-key-CHANGE-THIS-IN-PRODUCTION
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=production-refresh-secret-key-CHANGE-THIS
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_SALT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=https://sonali-app.com
